@echo off 
title Installing Future Fuel Management System 
color 0A 
 
echo ============================================= 
echo    Future Fuel Management System Installer 
echo                Version 2.2.0 
echo ============================================= 
echo. 
echo [1/4] Creating installation directory... 
set INSTALL_DIR=%USERPROFILE%\Future Fuel Management 
if exist "%INSTALL_DIR%" rmdir /s /q "%INSTALL_DIR%" 
mkdir "%INSTALL_DIR%" 
echo OK: Directory created 
echo. 
echo [2/4] Installing application files... 
xcopy /E /I /H /Y "app\*" "%INSTALL_DIR%\" 
echo OK: Files installed 
echo. 
echo [3/4] Creating desktop shortcut... 
echo Set oWS = WScript.CreateObject("WScript.Shell") 
echo sLinkFile = "%USERPROFILE%\Desktop\Future Fuel Management.lnk" 
echo Set oLink = oWS.CreateShortcut(sLinkFile) 
echo oLink.TargetPath = "%INSTALL_DIR%\index.html" 
echo oLink.WorkingDirectory = "%INSTALL_DIR%" 
echo oLink.Description = "Future Fuel Management System" 
echo oLink.IconLocation = "%INSTALL_DIR%\assets\icons\app-icon.ico" 
echo oLink.Save 
cscript //nologo "%TEMP%\shortcut.vbs" 
del "%TEMP%\shortcut.vbs" 
echo OK: Desktop shortcut created 
echo. 
echo [4/4] Creating start menu entry... 
mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Future Fuel" 
echo Set oWS = WScript.CreateObject("WScript.Shell") 
echo sLinkFile = "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Future Fuel\Future Fuel Management.lnk" 
echo Set oLink = oWS.CreateShortcut(sLinkFile) 
echo oLink.TargetPath = "%INSTALL_DIR%\index.html" 
echo oLink.WorkingDirectory = "%INSTALL_DIR%" 
echo oLink.Description = "Future Fuel Management System" 
echo oLink.IconLocation = "%INSTALL_DIR%\assets\icons\app-icon.ico" 
echo oLink.Save 
cscript //nologo "%TEMP%\startmenu.vbs" 
del "%TEMP%\startmenu.vbs" 
echo OK: Start menu entry created 
echo. 
echo ============================================= 
echo        Installation Completed Successfully! 
echo ============================================= 
echo. 
echo Application installed to: %INSTALL_DIR% 
echo Desktop shortcut created 
echo Start menu entry created 
echo. 
echo Press any key to launch the application... 
pause >nul 
start "" "%INSTALL_DIR%\index.html" 
