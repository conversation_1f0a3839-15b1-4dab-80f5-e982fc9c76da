// Future Fuel Corporation - Crypto Utilities
// نظام إدارة مؤسسة وقود المستقبل - أدوات التشفير

const crypto = require('crypto');
const CryptoJS = require('crypto-js');
const config = require('../config/app.config');

class CryptoUtils {
    constructor() {
        this.algorithm = 'aes-256-gcm';
        this.keyLength = 32;
        this.ivLength = 16;
        this.tagLength = 16;
        this.secretKey = this.deriveKey(config.security.jwtSecret);
    }

    // Derive a key from the secret
    deriveKey(secret) {
        return crypto.pbkdf2Sync(secret, 'future-fuel-salt', 10000, this.keyLength, 'sha256');
    }

    // Generate random string
    generateRandomString(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    // Generate UUID
    generateUUID() {
        return crypto.randomUUID();
    }

    // Hash password using bcrypt-like method
    hashPassword(password, saltRounds = 10) {
        const bcrypt = require('bcryptjs');
        return bcrypt.hashSync(password, saltRounds);
    }

    // Verify password
    verifyPassword(password, hash) {
        const bcrypt = require('bcryptjs');
        return bcrypt.compareSync(password, hash);
    }

    // Encrypt data
    encrypt(text) {
        try {
            const iv = crypto.randomBytes(this.ivLength);
            const cipher = crypto.createCipher(this.algorithm, this.secretKey, { iv });
            
            let encrypted = cipher.update(text, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const tag = cipher.getAuthTag();
            
            return {
                encrypted: encrypted,
                iv: iv.toString('hex'),
                tag: tag.toString('hex')
            };
        } catch (error) {
            throw new Error('Encryption failed: ' + error.message);
        }
    }

    // Decrypt data
    decrypt(encryptedData) {
        try {
            const { encrypted, iv, tag } = encryptedData;
            
            const decipher = crypto.createDecipher(this.algorithm, this.secretKey, {
                iv: Buffer.from(iv, 'hex')
            });
            
            decipher.setAuthTag(Buffer.from(tag, 'hex'));
            
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            throw new Error('Decryption failed: ' + error.message);
        }
    }

    // Simple encrypt for client-side compatibility
    simpleEncrypt(text) {
        return CryptoJS.AES.encrypt(text, config.security.jwtSecret).toString();
    }

    // Simple decrypt for client-side compatibility
    simpleDecrypt(encryptedText) {
        const bytes = CryptoJS.AES.decrypt(encryptedText, config.security.jwtSecret);
        return bytes.toString(CryptoJS.enc.Utf8);
    }

    // Generate license key
    generateLicenseKey(prefix = 'FF') {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = prefix + '-';
        
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            if (i < 3) result += '-';
        }
        
        return result;
    }

    // Generate machine fingerprint
    generateMachineFingerprint(userAgent, screenResolution, timezone, language) {
        const fingerprint = [
            userAgent || '',
            screenResolution || '',
            timezone || '',
            language || '',
            Date.now().toString()
        ].join('|');
        
        return crypto.createHash('sha256').update(fingerprint).digest('hex').substring(0, 16);
    }

    // Hash sensitive data
    hashData(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    // Create HMAC signature
    createSignature(data, secret = null) {
        const key = secret || config.security.jwtSecret;
        return crypto.createHmac('sha256', key).update(data).digest('hex');
    }

    // Verify HMAC signature
    verifySignature(data, signature, secret = null) {
        const expectedSignature = this.createSignature(data, secret);
        return crypto.timingSafeEqual(
            Buffer.from(signature, 'hex'),
            Buffer.from(expectedSignature, 'hex')
        );
    }

    // Generate secure token
    generateSecureToken(length = 32) {
        return crypto.randomBytes(length).toString('base64url');
    }

    // Generate API key
    generateApiKey() {
        const timestamp = Date.now().toString();
        const random = this.generateRandomString(16);
        const combined = timestamp + random;
        return Buffer.from(combined).toString('base64url');
    }

    // Encrypt sensitive configuration
    encryptConfig(config) {
        try {
            const configString = JSON.stringify(config);
            return this.simpleEncrypt(configString);
        } catch (error) {
            throw new Error('Config encryption failed: ' + error.message);
        }
    }

    // Decrypt sensitive configuration
    decryptConfig(encryptedConfig) {
        try {
            const configString = this.simpleDecrypt(encryptedConfig);
            return JSON.parse(configString);
        } catch (error) {
            throw new Error('Config decryption failed: ' + error.message);
        }
    }

    // Generate session ID
    generateSessionId() {
        const timestamp = Date.now();
        const random = this.generateRandomString(16);
        return `session_${timestamp}_${random}`;
    }

    // Validate license key format
    validateLicenseKeyFormat(licenseKey) {
        const pattern = /^[A-Z]{2}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
        return pattern.test(licenseKey);
    }

    // Generate checksum
    generateChecksum(data) {
        return crypto.createHash('md5').update(data).digest('hex');
    }

    // Verify checksum
    verifyChecksum(data, expectedChecksum) {
        const actualChecksum = this.generateChecksum(data);
        return actualChecksum === expectedChecksum;
    }

    // Secure random number
    secureRandom(min, max) {
        const range = max - min + 1;
        const bytesNeeded = Math.ceil(Math.log2(range) / 8);
        const maxValue = Math.pow(256, bytesNeeded);
        const randomBytes = crypto.randomBytes(bytesNeeded);
        const randomValue = randomBytes.readUIntBE(0, bytesNeeded);
        
        if (randomValue >= maxValue - (maxValue % range)) {
            return this.secureRandom(min, max);
        }
        
        return min + (randomValue % range);
    }

    // Generate OTP
    generateOTP(length = 6) {
        let otp = '';
        for (let i = 0; i < length; i++) {
            otp += this.secureRandom(0, 9).toString();
        }
        return otp;
    }

    // Time-based OTP (TOTP)
    generateTOTP(secret, timeStep = 30) {
        const time = Math.floor(Date.now() / 1000 / timeStep);
        const timeBuffer = Buffer.alloc(8);
        timeBuffer.writeUInt32BE(time, 4);
        
        const hmac = crypto.createHmac('sha1', secret);
        hmac.update(timeBuffer);
        const hash = hmac.digest();
        
        const offset = hash[hash.length - 1] & 0x0f;
        const code = ((hash[offset] & 0x7f) << 24) |
                    ((hash[offset + 1] & 0xff) << 16) |
                    ((hash[offset + 2] & 0xff) << 8) |
                    (hash[offset + 3] & 0xff);
        
        return (code % 1000000).toString().padStart(6, '0');
    }

    // Verify TOTP
    verifyTOTP(token, secret, timeStep = 30, window = 1) {
        for (let i = -window; i <= window; i++) {
            const time = Math.floor(Date.now() / 1000 / timeStep) + i;
            const timeBuffer = Buffer.alloc(8);
            timeBuffer.writeUInt32BE(time, 4);
            
            const hmac = crypto.createHmac('sha1', secret);
            hmac.update(timeBuffer);
            const hash = hmac.digest();
            
            const offset = hash[hash.length - 1] & 0x0f;
            const code = ((hash[offset] & 0x7f) << 24) |
                        ((hash[offset + 1] & 0xff) << 16) |
                        ((hash[offset + 2] & 0xff) << 8) |
                        (hash[offset + 3] & 0xff);
            
            const expectedToken = (code % 1000000).toString().padStart(6, '0');
            
            if (crypto.timingSafeEqual(Buffer.from(token), Buffer.from(expectedToken))) {
                return true;
            }
        }
        
        return false;
    }
}

module.exports = new CryptoUtils();
