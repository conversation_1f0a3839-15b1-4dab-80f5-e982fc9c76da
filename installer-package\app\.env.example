# Future Fuel Corporation - Environment Configuration
# نظام إدارة مؤسسة وقود المستقبل - إعدادات البيئة

# Server Configuration
PORT=3000
NODE_ENV=production
HOST=127.0.0.1

# Security Configuration
JWT_SECRET=future-fuel-secret-key-2024-change-this-in-production
SESSION_SECRET=session-secret-key-change-this-too

# Database Configuration
DB_PATH=./data/app.db
BACKUP_DIR=./data/backups

# License Configuration
DEFAULT_LICENSE_DURATION=365
MAX_LICENSE_ACTIVATIONS=1
AUTO_APPROVE_REQUESTS=false

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Company Information
COMPANY_NAME=مؤسسة وقود المستقبل
COMPANY_NAME_EN=Future Fuel Corporation
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=https://futurefuel.sa
COMPANY_PHONE=+966-11-1234567

# Application Settings
APP_VERSION=2.2.0
APP_NAME=Future Fuel Management System
ENABLE_LOGGING=true
LOG_LEVEL=info

# Security Settings
ENABLE_RATE_LIMITING=true
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
SESSION_TIMEOUT=86400000

# Backup Settings
AUTO_BACKUP=true
BACKUP_INTERVAL=3600000
MAX_BACKUPS=10

# Development Settings (Only for development)
DEBUG=false
ENABLE_CORS=true
ALLOW_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
