@echo off
chcp 65001 >nul
title نظام إدارة مؤسسة وقود المستقبل v2.2.0
color 0A

:main
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  نظام إدارة مؤسسة وقود المستقبل                  ║
echo ║              Future Fuel Corporation Management              ║
echo ║                        الإصدار 2.2.0                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │                        خيارات التشغيل                         │
echo │                      Running Options                         │
echo └──────────────────────────────────────────────────────────────┘
echo.
echo [1] 🚀 تشغيل سريع (Quick Start)
echo [2] 💻 تطبيق سطح المكتب (Desktop App)
echo [3] 🌐 خادم ويب (Web Server)
echo [4] 👨‍💼 لوحة تحكم المطور (Admin Panel)
echo [5] 🔐 صفحة تسجيل الدخول (Login Page)
echo [6] ⚙️  إعداد وتثبيت (Setup & Install)
echo [7] 🔧 أدوات المطور (Developer Tools)
echo [8] 📊 معلومات النظام (System Info)
echo [9] ❌ خروج (Exit)
echo.

set /p choice="اختر رقم الخيار (1-9): "

if "%choice%"=="1" goto quick_start
if "%choice%"=="2" goto desktop_app
if "%choice%"=="3" goto web_server
if "%choice%"=="4" goto admin_panel
if "%choice%"=="5" goto login_page
if "%choice%"=="6" goto setup
if "%choice%"=="7" goto dev_tools
if "%choice%"=="8" goto system_info
if "%choice%"=="9" goto exit

echo.
echo ❌ خيار غير صحيح! Invalid choice!
timeout /t 2 >nul
goto main

:quick_start
cls
echo.
echo 🚀 تشغيل سريع للنظام...
echo Quick Starting System...
echo.
echo جاري فحص التبعيات...
echo Checking dependencies...

if not exist "node_modules" (
    echo.
    echo ⚠️  التبعيات غير مثبتة. جاري التثبيت...
    echo Dependencies not installed. Installing...
    call npm install
    if errorlevel 1 (
        echo.
        echo ❌ خطأ في تثبيت التبعيات!
        echo Error installing dependencies!
        pause
        goto main
    )
)

echo.
echo ✅ جاري تشغيل النظام...
echo Starting system...
echo.
echo 🌐 الخادم: http://localhost:3000
echo 🔐 تسجيل الدخول: http://localhost:3000/login.html
echo 👨‍💼 لوحة المطور: http://localhost:3000/admin.html
echo.
echo 📝 بيانات الدخول الافتراضية:
echo Default login credentials:
echo Username: admin
echo Password: admin123
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo Press Ctrl+C to stop the server
echo.

npm start
goto main

:desktop_app
cls
echo.
echo 💻 تشغيل تطبيق سطح المكتب...
echo Starting Desktop Application...
echo.

if not exist "node_modules" (
    echo ⚠️  يجب تثبيت التبعيات أولاً!
    echo Dependencies must be installed first!
    echo.
    set /p install="هل تريد تثبيت التبعيات الآن؟ (y/n): "
    if /i "%install%"=="y" (
        call npm install
        if errorlevel 1 (
            echo ❌ فشل في تثبيت التبعيات!
            pause
            goto main
        )
    ) else (
        goto main
    )
)

npm run electron
goto main

:web_server
cls
echo.
echo 🌐 تشغيل خادم الويب...
echo Starting Web Server...
echo.
echo الخادم سيعمل على: http://localhost:3000
echo Server will run on: http://localhost:3000
echo.

npm run web
goto main

:admin_panel
cls
echo.
echo 👨‍💼 فتح لوحة تحكم المطور...
echo Opening Admin Panel...
echo.

start http://localhost:3000/admin.html
echo.
echo ✅ تم فتح لوحة التحكم في المتصفح
echo Admin panel opened in browser
echo.
echo إذا لم تفتح تلقائياً، اذهب إلى:
echo If it didn't open automatically, go to:
echo http://localhost:3000/admin.html
echo.
pause
goto main

:login_page
cls
echo.
echo 🔐 فتح صفحة تسجيل الدخول...
echo Opening Login Page...
echo.

start http://localhost:3000/login.html
echo.
echo ✅ تم فتح صفحة تسجيل الدخول في المتصفح
echo Login page opened in browser
echo.
pause
goto main

:setup
cls
echo.
echo ⚙️  إعداد وتثبيت النظام...
echo System Setup & Installation...
echo.

echo [1] تثبيت التبعيات (Install Dependencies)
echo [2] إعداد قاعدة البيانات (Setup Database)
echo [3] إنشاء ملف الإعدادات (Create Config File)
echo [4] تشغيل الإعداد الكامل (Full Setup)
echo [5] العودة للقائمة الرئيسية (Back to Main Menu)
echo.

set /p setup_choice="اختر خيار الإعداد (1-5): "

if "%setup_choice%"=="1" (
    echo.
    echo 📦 جاري تثبيت التبعيات...
    echo Installing dependencies...
    npm install
    echo ✅ تم تثبيت التبعيات بنجاح!
    pause
)

if "%setup_choice%"=="2" (
    echo.
    echo 🗄️  جاري إعداد قاعدة البيانات...
    echo Setting up database...
    node -e "require('./server/database/init').initializeDatabase().then(() => console.log('✅ Database setup completed!'))"
    pause
)

if "%setup_choice%"=="3" (
    echo.
    echo ⚙️  جاري إنشاء ملف الإعدادات...
    echo Creating configuration file...
    if not exist ".env" (
        copy ".env.example" ".env"
        echo ✅ تم إنشاء ملف .env
    ) else (
        echo ⚠️  ملف .env موجود مسبقاً
    )
    pause
)

if "%setup_choice%"=="4" (
    echo.
    echo 🔧 تشغيل الإعداد الكامل...
    echo Running full setup...
    call npm install
    if not exist ".env" copy ".env.example" ".env"
    node -e "require('./server/database/init').initializeDatabase().then(() => console.log('✅ Full setup completed!'))"
    echo.
    echo ✅ تم الإعداد الكامل بنجاح!
    echo Full setup completed successfully!
    pause
)

if "%setup_choice%"=="5" goto main

goto setup

:dev_tools
cls
echo.
echo 🔧 أدوات المطور...
echo Developer Tools...
echo.

echo [1] عرض السجلات (View Logs)
echo [2] تنظيف الملفات المؤقتة (Clean Temp Files)
echo [3] إنشاء نسخة احتياطية (Create Backup)
echo [4] فحص النظام (System Check)
echo [5] إعادة تعيين قاعدة البيانات (Reset Database)
echo [6] العودة للقائمة الرئيسية (Back to Main Menu)
echo.

set /p dev_choice="اختر أداة المطور (1-6): "

if "%dev_choice%"=="1" (
    echo.
    echo 📋 عرض السجلات...
    if exist "logs\app.log" (
        type "logs\app.log"
    ) else (
        echo لا توجد سجلات متاحة
    )
    pause
)

if "%dev_choice%"=="2" (
    echo.
    echo 🧹 تنظيف الملفات المؤقتة...
    if exist "data\temp" rmdir /s /q "data\temp"
    if exist "logs\*.log" del /q "logs\*.log"
    echo ✅ تم تنظيف الملفات المؤقتة
    pause
)

if "%dev_choice%"=="3" (
    echo.
    echo 💾 إنشاء نسخة احتياطية...
    node -e "require('./utils/backup').createFullBackup('Manual backup').then(() => console.log('✅ Backup created!'))"
    pause
)

if "%dev_choice%"=="4" (
    echo.
    echo 🔍 فحص النظام...
    echo Node.js Version:
    node --version
    echo NPM Version:
    npm --version
    echo.
    echo فحص الملفات المطلوبة...
    if exist "server.js" echo ✅ server.js
    if exist "main.js" echo ✅ main.js
    if exist "package.json" echo ✅ package.json
    if exist "login.html" echo ✅ login.html
    if exist "admin.html" echo ✅ admin.html
    pause
)

if "%dev_choice%"=="5" (
    echo.
    echo ⚠️  تحذير: سيتم حذف جميع البيانات!
    echo Warning: All data will be deleted!
    set /p confirm="هل أنت متأكد؟ (yes/no): "
    if /i "%confirm%"=="yes" (
        if exist "data\app.db" del "data\app.db"
        echo ✅ تم إعادة تعيين قاعدة البيانات
    )
    pause
)

if "%dev_choice%"=="6" goto main

goto dev_tools

:system_info
cls
echo.
echo 📊 معلومات النظام...
echo System Information...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        معلومات التطبيق                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo الاسم: نظام إدارة مؤسسة وقود المستقبل
echo Name: Future Fuel Corporation Management System
echo الإصدار: 2.2.0
echo Version: 2.2.0
echo التاريخ: 27/05/2025
echo Date: 27/05/2025
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        المميزات الجديدة                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo ✅ نظام مصادقة متقدم
echo ✅ إدارة التراخيص عن بُعد
echo ✅ واجهة تسجيل دخول عصرية
echo ✅ لوحة تحكم المطور
echo ✅ تشفير وأمان محسن
echo ✅ ربط التراخيص بمعرف الجهاز
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        معلومات النظام                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo نظام التشغيل: %OS%
echo المعالج: %PROCESSOR_ARCHITECTURE%
echo المستخدم: %USERNAME%
echo المجلد الحالي: %CD%
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                         الدعم الفني                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo البريد الإلكتروني: <EMAIL>
echo الموقع: https://futurefuel.sa
echo.
pause
goto main

:exit
cls
echo.
echo 👋 شكراً لاستخدام نظام وقود المستقبل!
echo Thank you for using Future Fuel System!
echo.
echo © 2024 Future Fuel Corporation
echo جميع الحقوق محفوظة - All Rights Reserved
echo.
timeout /t 3 >nul
exit
