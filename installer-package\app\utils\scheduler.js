// Future Fuel Corporation - Task Scheduler
// نظام إدارة مؤسسة وقود المستقبل - مجدول المهام

const { getDatabase } = require('../server/database/init');
const logger = require('./logger');
const notifications = require('./notifications');
const backup = require('./backup');
const config = require('../config/app.config');

class TaskScheduler {
    constructor() {
        this.tasks = new Map();
        this.intervals = new Map();
        this.isRunning = false;
        
        this.initializeScheduler();
    }

    async initializeScheduler() {
        try {
            logger.info('Initializing task scheduler...');
            
            // Register default tasks
            this.registerDefaultTasks();
            
            // Start scheduler
            this.start();
            
            logger.info('Task scheduler initialized successfully');
            
        } catch (error) {
            logger.error('Failed to initialize task scheduler', { error: error.message });
        }
    }

    registerDefaultTasks() {
        // Check license expiration daily
        this.registerTask('checkLicenseExpiration', {
            name: 'فحص انتهاء التراخيص',
            description: 'Check for expiring licenses and send notifications',
            interval: 24 * 60 * 60 * 1000, // 24 hours
            enabled: true,
            handler: this.checkLicenseExpiration.bind(this)
        });

        // Clean expired sessions hourly
        this.registerTask('cleanExpiredSessions', {
            name: 'تنظيف الجلسات المنتهية',
            description: 'Clean up expired user sessions',
            interval: 60 * 60 * 1000, // 1 hour
            enabled: true,
            handler: this.cleanExpiredSessions.bind(this)
        });

        // Database maintenance weekly
        this.registerTask('databaseMaintenance', {
            name: 'صيانة قاعدة البيانات',
            description: 'Perform database optimization and cleanup',
            interval: 7 * 24 * 60 * 60 * 1000, // 7 days
            enabled: true,
            handler: this.databaseMaintenance.bind(this)
        });

        // Generate reports monthly
        this.registerTask('generateMonthlyReports', {
            name: 'تقارير شهرية',
            description: 'Generate monthly usage and license reports',
            interval: 30 * 24 * 60 * 60 * 1000, // 30 days
            enabled: true,
            handler: this.generateMonthlyReports.bind(this)
        });

        // Check system health every 5 minutes
        this.registerTask('systemHealthCheck', {
            name: 'فحص صحة النظام',
            description: 'Monitor system health and performance',
            interval: 5 * 60 * 1000, // 5 minutes
            enabled: true,
            handler: this.systemHealthCheck.bind(this)
        });

        // Backup data (if auto backup is enabled)
        if (config.database.autoBackup) {
            this.registerTask('autoBackup', {
                name: 'نسخ احتياطي تلقائي',
                description: 'Create automatic backups',
                interval: config.database.backupInterval,
                enabled: true,
                handler: this.autoBackup.bind(this)
            });
        }
    }

    registerTask(id, taskConfig) {
        this.tasks.set(id, {
            id: id,
            ...taskConfig,
            lastRun: null,
            nextRun: null,
            runCount: 0,
            errors: 0,
            lastError: null
        });

        logger.info('Task registered', { id: id, name: taskConfig.name });
    }

    start() {
        if (this.isRunning) {
            return;
        }

        this.isRunning = true;

        // Start all enabled tasks
        this.tasks.forEach((task, id) => {
            if (task.enabled) {
                this.startTask(id);
            }
        });

        logger.info('Task scheduler started');
    }

    stop() {
        if (!this.isRunning) {
            return;
        }

        this.isRunning = false;

        // Stop all running intervals
        this.intervals.forEach((intervalId, taskId) => {
            clearInterval(intervalId);
            this.intervals.delete(taskId);
        });

        logger.info('Task scheduler stopped');
    }

    startTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task || !task.enabled) {
            return;
        }

        // Clear existing interval if any
        if (this.intervals.has(taskId)) {
            clearInterval(this.intervals.get(taskId));
        }

        // Set next run time
        task.nextRun = new Date(Date.now() + task.interval);

        // Start interval
        const intervalId = setInterval(async () => {
            await this.runTask(taskId);
        }, task.interval);

        this.intervals.set(taskId, intervalId);

        logger.info('Task started', { id: taskId, name: task.name, interval: task.interval });
    }

    stopTask(taskId) {
        const intervalId = this.intervals.get(taskId);
        if (intervalId) {
            clearInterval(intervalId);
            this.intervals.delete(taskId);
        }

        const task = this.tasks.get(taskId);
        if (task) {
            task.nextRun = null;
        }

        logger.info('Task stopped', { id: taskId });
    }

    async runTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) {
            return;
        }

        try {
            logger.debug('Running task', { id: taskId, name: task.name });

            const startTime = Date.now();
            task.lastRun = new Date();
            task.nextRun = new Date(Date.now() + task.interval);

            // Execute task handler
            await task.handler();

            const duration = Date.now() - startTime;
            task.runCount++;

            logger.info('Task completed successfully', {
                id: taskId,
                name: task.name,
                duration: duration,
                runCount: task.runCount
            });

        } catch (error) {
            task.errors++;
            task.lastError = {
                message: error.message,
                timestamp: new Date(),
                stack: error.stack
            };

            logger.error('Task execution failed', {
                id: taskId,
                name: task.name,
                error: error.message,
                errors: task.errors
            });

            // Disable task if too many errors
            if (task.errors >= 5) {
                task.enabled = false;
                this.stopTask(taskId);
                
                logger.warn('Task disabled due to repeated failures', {
                    id: taskId,
                    name: task.name,
                    errors: task.errors
                });
            }
        }
    }

    // Task handlers
    async checkLicenseExpiration() {
        try {
            const db = getDatabase();
            const warningDays = [30, 7, 1]; // Warn at 30, 7, and 1 days before expiration

            for (const days of warningDays) {
                const checkDate = new Date();
                checkDate.setDate(checkDate.getDate() + days);

                const query = `
                    SELECT l.*, u.username, u.full_name, u.email 
                    FROM licenses l 
                    LEFT JOIN users u ON l.user_id = u.id 
                    WHERE l.status = 'active' 
                    AND l.expires_at IS NOT NULL 
                    AND DATE(l.expires_at) = DATE(?)
                `;

                db.all(query, [checkDate.toISOString()], async (err, licenses) => {
                    if (err) {
                        logger.error('Failed to check license expiration', { error: err.message });
                        return;
                    }

                    for (const license of licenses) {
                        await notifications.notifyLicenseExpiring(license, days);
                    }
                });
            }

        } catch (error) {
            throw error;
        }
    }

    async cleanExpiredSessions() {
        try {
            const db = getDatabase();

            db.run(
                'UPDATE sessions SET is_active = 0 WHERE expires_at < CURRENT_TIMESTAMP',
                function(err) {
                    if (err) {
                        logger.error('Failed to clean expired sessions', { error: err.message });
                        return;
                    }

                    if (this.changes > 0) {
                        logger.info('Expired sessions cleaned', { count: this.changes });
                    }
                }
            );

        } catch (error) {
            throw error;
        }
    }

    async databaseMaintenance() {
        try {
            const db = getDatabase();

            // Vacuum database
            db.run('VACUUM', (err) => {
                if (err) {
                    logger.error('Database vacuum failed', { error: err.message });
                } else {
                    logger.info('Database vacuum completed');
                }
            });

            // Analyze database
            db.run('ANALYZE', (err) => {
                if (err) {
                    logger.error('Database analyze failed', { error: err.message });
                } else {
                    logger.info('Database analyze completed');
                }
            });

            // Clean old audit logs (older than 90 days)
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 90);

            db.run(
                'DELETE FROM audit_log WHERE created_at < ?',
                [cutoffDate.toISOString()],
                function(err) {
                    if (err) {
                        logger.error('Failed to clean old audit logs', { error: err.message });
                    } else if (this.changes > 0) {
                        logger.info('Old audit logs cleaned', { count: this.changes });
                    }
                }
            );

        } catch (error) {
            throw error;
        }
    }

    async generateMonthlyReports() {
        try {
            // TODO: Implement monthly report generation
            logger.info('Monthly reports generation started');
            
            // This would generate reports about:
            // - License usage statistics
            // - User activity
            // - System performance
            // - Security events
            
            logger.info('Monthly reports generation completed');

        } catch (error) {
            throw error;
        }
    }

    async systemHealthCheck() {
        try {
            const health = {
                timestamp: new Date(),
                database: false,
                memory: process.memoryUsage(),
                uptime: process.uptime(),
                cpu: process.cpuUsage()
            };

            // Check database connectivity
            const db = getDatabase();
            db.get('SELECT 1', (err) => {
                health.database = !err;
                
                if (err) {
                    logger.error('Database health check failed', { error: err.message });
                }
            });

            // Check memory usage
            const memoryUsage = health.memory.heapUsed / health.memory.heapTotal;
            if (memoryUsage > 0.9) {
                logger.warn('High memory usage detected', { usage: memoryUsage });
            }

            logger.debug('System health check completed', health);

        } catch (error) {
            throw error;
        }
    }

    async autoBackup() {
        try {
            await backup.createFullBackup('Scheduled automatic backup');
        } catch (error) {
            throw error;
        }
    }

    // Management methods
    getTaskStatus(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) {
            return null;
        }

        return {
            id: task.id,
            name: task.name,
            description: task.description,
            enabled: task.enabled,
            lastRun: task.lastRun,
            nextRun: task.nextRun,
            runCount: task.runCount,
            errors: task.errors,
            lastError: task.lastError,
            isRunning: this.intervals.has(taskId)
        };
    }

    getAllTasksStatus() {
        const status = [];
        this.tasks.forEach((task, id) => {
            status.push(this.getTaskStatus(id));
        });
        return status;
    }

    enableTask(taskId) {
        const task = this.tasks.get(taskId);
        if (task) {
            task.enabled = true;
            if (this.isRunning) {
                this.startTask(taskId);
            }
            logger.info('Task enabled', { id: taskId, name: task.name });
        }
    }

    disableTask(taskId) {
        const task = this.tasks.get(taskId);
        if (task) {
            task.enabled = false;
            this.stopTask(taskId);
            logger.info('Task disabled', { id: taskId, name: task.name });
        }
    }

    async runTaskNow(taskId) {
        await this.runTask(taskId);
    }
}

module.exports = new TaskScheduler();
