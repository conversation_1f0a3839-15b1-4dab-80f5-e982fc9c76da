const express = require('express');
const bcrypt = require('bcryptjs');
const { getDatabase } = require('../database/init');
const { generateToken, authenticateToken, auditLog } = require('../middleware/auth');

const router = express.Router();

// تسجيل الدخول
router.post('/login', auditLog('login'), async (req, res) => {
    try {
        const { username, password, machineId } = req.body;

        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: 'اسم المستخدم وكلمة المرور مطلوبان'
            });
        }

        const db = getDatabase();

        // البحث عن المستخدم
        db.get('SELECT * FROM users WHERE username = ? AND is_active = 1', 
            [username], async (err, user) => {
                
            if (err) {
                console.error('خطأ في البحث عن المستخدم:', err);
                return res.status(500).json({
                    success: false,
                    message: 'خطأ في الخادم'
                });
            }

            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
                });
            }

            // التحقق من القفل
            if (user.locked_until && new Date(user.locked_until) > new Date()) {
                return res.status(423).json({
                    success: false,
                    message: 'الحساب مقفل مؤقتاً. حاول مرة أخرى لاحقاً'
                });
            }

            // التحقق من كلمة المرور
            const isValidPassword = await bcrypt.compare(password, user.password_hash);

            if (!isValidPassword) {
                // زيادة عدد محاولات الدخول الفاشلة
                const newAttempts = (user.login_attempts || 0) + 1;
                let lockUntil = null;

                // قفل الحساب بعد 5 محاولات فاشلة
                if (newAttempts >= 5) {
                    lockUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 دقيقة
                }

                db.run('UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?',
                    [newAttempts, lockUntil, user.id]);

                return res.status(401).json({
                    success: false,
                    message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
                    attemptsLeft: Math.max(0, 5 - newAttempts)
                });
            }

            // البحث عن ترخيص نشط للمستخدم (إلا إذا كان مدير)
            if (user.role === 'admin') {
                // المدير لا يحتاج ترخيص
                const token = generateToken(user, null);

                // تحديث آخر دخول وإعادة تعيين محاولات الدخول
                db.run(`UPDATE users SET
                        last_login = CURRENT_TIMESTAMP,
                        login_attempts = 0,
                        locked_until = NULL
                        WHERE id = ?`, [user.id]);

                // إنشاء جلسة جديدة
                const sessionId = 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
                const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 ساعة

                db.run(`INSERT INTO sessions
                        (session_id, user_id, license_id, expires_at, ip_address, user_agent)
                        VALUES (?, ?, ?, ?, ?, ?)`,
                    [sessionId, user.id, null, expiresAt,
                     req.ip, req.headers['user-agent']]);

                return res.json({
                    success: true,
                    message: 'تم تسجيل الدخول بنجاح',
                    data: {
                        token: token,
                        sessionId: sessionId,
                        user: {
                            id: user.id,
                            username: user.username,
                            full_name: user.full_name,
                            role: user.role,
                            email: user.email
                        },
                        license: null
                    }
                });
            }

            // للمستخدمين العاديين - البحث عن ترخيص نشط
            db.get(`SELECT * FROM licenses
                    WHERE user_id = ? AND status = 'active'
                    AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
                    ORDER BY created_at DESC LIMIT 1`,
                [user.id], (err, license) => {

                if (err) {
                    console.error('خطأ في البحث عن الترخيص:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'خطأ في التحقق من الترخيص'
                    });
                }

                // إنشاء التوكن
                const token = generateToken(user, license);

                // تحديث آخر دخول وإعادة تعيين محاولات الدخول
                db.run(`UPDATE users SET 
                        last_login = CURRENT_TIMESTAMP, 
                        login_attempts = 0, 
                        locked_until = NULL 
                        WHERE id = ?`, [user.id]);

                // إنشاء جلسة جديدة
                const sessionId = 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
                const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 ساعة

                db.run(`INSERT INTO sessions 
                        (session_id, user_id, license_id, expires_at, ip_address, user_agent) 
                        VALUES (?, ?, ?, ?, ?, ?)`,
                    [sessionId, user.id, license ? license.id : null, expiresAt, 
                     req.ip, req.headers['user-agent']]);

                // تحديث معلومات الجهاز في الترخيص إذا كان متوفراً
                if (license && machineId) {
                    db.run('UPDATE licenses SET machine_id = ? WHERE id = ?', 
                        [machineId, license.id]);
                }

                res.json({
                    success: true,
                    message: 'تم تسجيل الدخول بنجاح',
                    data: {
                        token: token,
                        sessionId: sessionId,
                        user: {
                            id: user.id,
                            username: user.username,
                            full_name: user.full_name,
                            role: user.role,
                            email: user.email
                        },
                        license: license ? {
                            id: license.id,
                            type: license.license_type,
                            expires_at: license.expires_at,
                            features: license.features ? JSON.parse(license.features) : null
                        } : null
                    }
                });
            });
        });

    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في الخادم'
        });
    }
});

// تسجيل الخروج
router.post('/logout', authenticateToken, auditLog('logout'), (req, res) => {
    try {
        const sessionId = req.headers['x-session-id'];
        
        if (sessionId) {
            const db = getDatabase();
            db.run('UPDATE sessions SET is_active = 0 WHERE session_id = ?', [sessionId]);
        }

        res.json({
            success: true,
            message: 'تم تسجيل الخروج بنجاح'
        });

    } catch (error) {
        console.error('خطأ في تسجيل الخروج:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في الخادم'
        });
    }
});

// التحقق من صحة التوكن
router.get('/verify', authenticateToken, (req, res) => {
    res.json({
        success: true,
        message: 'التوكن صالح',
        user: req.user
    });
});

// طلب ترخيص جديد
router.post('/request-license', auditLog('request_license'), async (req, res) => {
    try {
        const { username, email, full_name, company, phone, machineId, notes } = req.body;

        if (!username || !email || !full_name || !machineId) {
            return res.status(400).json({
                success: false,
                message: 'جميع الحقول المطلوبة يجب ملؤها'
            });
        }

        const db = getDatabase();

        // التحقق من عدم وجود المستخدم مسبقاً
        db.get('SELECT id FROM users WHERE username = ? OR email = ?', 
            [username, email], async (err, existingUser) => {
                
            if (err) {
                console.error('خطأ في التحقق من المستخدم:', err);
                return res.status(500).json({
                    success: false,
                    message: 'خطأ في الخادم'
                });
            }

            if (existingUser) {
                return res.status(409).json({
                    success: false,
                    message: 'اسم المستخدم أو البريد الإلكتروني مستخدم مسبقاً'
                });
            }

            // إنشاء كلمة مرور مؤقتة
            const tempPassword = Math.random().toString(36).slice(-8);
            const hashedPassword = await bcrypt.hash(tempPassword, 10);

            // إنشاء المستخدم الجديد
            db.run(`INSERT INTO users 
                    (username, email, password_hash, full_name, role, is_active) 
                    VALUES (?, ?, ?, ?, 'user', 0)`,
                [username, email, hashedPassword, full_name], function(err) {
                    
                if (err) {
                    console.error('خطأ في إنشاء المستخدم:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'خطأ في إنشاء المستخدم'
                    });
                }

                const userId = this.lastID;

                // إنشاء مفتاح ترخيص
                const licenseKey = generateLicenseKey();

                // إنشاء طلب ترخيص
                const requestNotes = JSON.stringify({
                    company: company,
                    phone: phone,
                    machine_id: machineId,
                    user_notes: notes,
                    temp_password: tempPassword
                });

                db.run(`INSERT INTO licenses 
                        (license_key, user_id, machine_id, license_type, status, notes) 
                        VALUES (?, ?, ?, 'trial', 'pending', ?)`,
                    [licenseKey, userId, machineId, requestNotes], (err) => {
                        
                    if (err) {
                        console.error('خطأ في إنشاء الترخيص:', err);
                        return res.status(500).json({
                            success: false,
                            message: 'خطأ في إنشاء طلب الترخيص'
                        });
                    }

                    res.json({
                        success: true,
                        message: 'تم إرسال طلب الترخيص بنجاح',
                        data: {
                            license_key: licenseKey,
                            temp_password: tempPassword,
                            message: 'سيتم مراجعة طلبك وتفعيل الترخيص قريباً'
                        }
                    });
                });
            });
        });

    } catch (error) {
        console.error('خطأ في طلب الترخيص:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في الخادم'
        });
    }
});

// دالة لإنشاء مفتاح ترخيص
function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    
    for (let i = 0; i < 4; i++) {
        for (let j = 0; j < 4; j++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        if (i < 3) result += '-';
    }
    
    return 'FF-' + result; // FF = Future Fuel
}

module.exports = router;
