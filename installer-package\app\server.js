const express = require('express');
const cors = require('cors');
const compression = require('compression');
const helmet = require('helmet');
const path = require('path');
const fs = require('fs');
const open = require('open');

// Import utilities and configuration
const config = require('./config/app.config');
const logger = require('./utils/logger');
const scheduler = require('./utils/scheduler');
const notifications = require('./utils/notifications');

// Import custom modules
const authRoutes = require('./server/routes/auth');
const licenseRoutes = require('./server/routes/license');
const dataRoutes = require('./server/routes/data');
const notificationRoutes = require('./server/routes/notifications');
const adminRoutes = require('./server/routes/admin');
const { initializeDatabase } = require('./server/database/init');
const { authenticateToken } = require('./server/middleware/auth');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: false, // Disable for development
    crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true
}));

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Static files
app.use(express.static(path.join(__dirname)));
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/license', licenseRoutes);
app.use('/api/data', authenticateToken, dataRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/admin', adminRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        version: '2.2.0'
    });
});

// Serve main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Server Error:', err);
    res.status(500).json({ 
        success: false, 
        message: 'خطأ داخلي في الخادم',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ 
        success: false, 
        message: 'الصفحة غير موجودة' 
    });
});

// Initialize database and start server
async function startServer() {
    try {
        console.log('🔧 تهيئة قاعدة البيانات...');
        await initializeDatabase();
        console.log('✅ تم تهيئة قاعدة البيانات بنجاح');

        // Initialize notifications table
        console.log('🔔 تهيئة نظام الإشعارات...');
        await notifications.initializeTable();
        console.log('✅ تم تهيئة نظام الإشعارات بنجاح');

        // Start task scheduler
        console.log('⏰ تشغيل مجدول المهام...');
        // Scheduler is auto-initialized
        console.log('✅ تم تشغيل مجدول المهام بنجاح');

        const server = app.listen(PORT, '127.0.0.1', () => {
            console.log(`🚀 الخادم يعمل على: http://127.0.0.1:${PORT}`);
            console.log('📱 نظام إدارة مؤسسة وقود المستقبل');
            console.log('🔐 نظام التراخيص والمصادقة مفعل');
            
            // Open browser in development mode
            if (process.env.NODE_ENV !== 'production') {
                setTimeout(() => {
                    open(`http://127.0.0.1:${PORT}`);
                }, 1000);
            }
        });

        // Graceful shutdown
        process.on('SIGTERM', () => {
            console.log('🛑 إيقاف الخادم...');
            server.close(() => {
                console.log('✅ تم إيقاف الخادم بنجاح');
                process.exit(0);
            });
        });

        process.on('SIGINT', () => {
            console.log('🛑 إيقاف الخادم...');
            server.close(() => {
                console.log('✅ تم إيقاف الخادم بنجاح');
                process.exit(0);
            });
        });

    } catch (error) {
        console.error('❌ خطأ في بدء تشغيل الخادم:', error);
        process.exit(1);
    }
}

// Start the server
startServer();

module.exports = app;
