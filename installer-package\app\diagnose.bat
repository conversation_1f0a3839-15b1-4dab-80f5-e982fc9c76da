@echo off
chcp 65001 >nul
title تشخيص مشاكل نظام وقود المستقبل
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    تشخيص مشاكل النظام                       ║
echo ║                  System Diagnostics Tool                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص متطلبات النظام...
echo Checking system requirements...
echo.

:: Check Node.js
echo [1/6] فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo    يرجى تثبيت Node.js من: https://nodejs.org/
) else (
    echo ✅ Node.js مثبت
    node --version
)

echo.

:: Check npm
echo [2/6] فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متاح
) else (
    echo ✅ npm متاح
    npm --version
)

echo.

:: Check package.json
echo [3/6] فحص ملف package.json...
if exist "package.json" (
    echo ✅ ملف package.json موجود
) else (
    echo ❌ ملف package.json غير موجود
)

echo.

:: Check node_modules
echo [4/6] فحص التبعيات...
if exist "node_modules" (
    echo ✅ مجلد node_modules موجود
    
    :: Check key dependencies
    if exist "node_modules\express" (
        echo   ✅ Express مثبت
    ) else (
        echo   ❌ Express غير مثبت
    )
    
    if exist "node_modules\sqlite3" (
        echo   ✅ SQLite3 مثبت
    ) else (
        echo   ❌ SQLite3 غير مثبت
    )
    
    if exist "node_modules\bcryptjs" (
        echo   ✅ bcryptjs مثبت
    ) else (
        echo   ❌ bcryptjs غير مثبت
    )
    
) else (
    echo ❌ مجلد node_modules غير موجود
    echo   يرجى تشغيل: npm install
)

echo.

:: Check database
echo [5/6] فحص قاعدة البيانات...
if exist "data" (
    echo ✅ مجلد data موجود
    if exist "data\app.db" (
        echo ✅ قاعدة البيانات موجودة
    ) else (
        echo ⚠️  قاعدة البيانات غير موجودة (سيتم إنشاؤها تلقائياً)
    )
) else (
    echo ⚠️  مجلد data غير موجود (سيتم إنشاؤه تلقائياً)
)

echo.

:: Check server files
echo [6/6] فحص ملفات الخادم...
if exist "server.js" (
    echo ✅ server.js موجود
) else (
    echo ❌ server.js غير موجود
)

if exist "login.html" (
    echo ✅ login.html موجود
) else (
    echo ❌ login.html غير موجود
)

if exist "admin.html" (
    echo ✅ admin.html موجود
) else (
    echo ❌ admin.html غير موجود
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      اختبار الاتصال                         ║
echo ║                   Connection Test                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🌐 اختبار تشغيل الخادم...
echo Testing server startup...

:: Try to start server for testing
echo جاري تشغيل الخادم للاختبار...
start /min cmd /c "node server.js"

:: Wait for server to start
timeout /t 5 >nul

:: Test connection
echo اختبار الاتصال...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/api/health' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ الخادم يعمل بشكل صحيح' -ForegroundColor Green; exit 0 } else { Write-Host '❌ الخادم لا يستجيب' -ForegroundColor Red; exit 1 } } catch { Write-Host '❌ خطأ في الاتصال بالخادم' -ForegroundColor Red; exit 1 }"

if errorlevel 1 (
    echo.
    echo 🔧 محاولة إصلاح المشاكل...
    echo Attempting to fix issues...
    
    :: Install dependencies if missing
    if not exist "node_modules" (
        echo تثبيت التبعيات...
        call npm install
    )
    
    :: Create directories if missing
    if not exist "data" mkdir "data"
    if not exist "logs" mkdir "logs"
    
    echo.
    echo ✅ تم إصلاح المشاكل الأساسية
    echo Basic issues have been fixed
)

:: Stop test server
taskkill /f /im node.exe >nul 2>&1

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      نتائج التشخيص                          ║
echo ║                   Diagnostic Results                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 ملخص الحالة:
echo Status Summary:
echo.

:: Final recommendations
echo 💡 التوصيات:
echo Recommendations:
echo.

if not exist "node_modules" (
    echo 1. قم بتشغيل: npm install
    echo    Run: npm install
)

echo 2. للتشغيل السريع: quick-start.bat
echo    For quick start: quick-start.bat
echo.
echo 3. للتشغيل التفاعلي: run.bat
echo    For interactive start: run.bat
echo.

echo 📞 الدعم الفني:
echo Technical Support:
echo    البريد الإلكتروني: <EMAIL>
echo    Email: <EMAIL>
echo.

echo 🔐 بيانات الدخول الافتراضية:
echo Default login credentials:
echo    اسم المستخدم / Username: admin
echo    كلمة المرور / Password: admin123
echo.

pause
