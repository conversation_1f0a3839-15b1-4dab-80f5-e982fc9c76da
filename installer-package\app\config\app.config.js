// Future Fuel Corporation - Application Configuration
// نظام إدارة مؤسسة وقود المستقبل - إعدادات التطبيق

const path = require('path');

module.exports = {
    // Application Information
    app: {
        name: 'Future Fuel Management System',
        nameAr: 'نظام إدارة مؤسسة وقود المستقبل',
        version: '2.2.0',
        description: 'Comprehensive fuel station management system',
        author: 'Future Fuel Corporation',
        website: 'https://futurefuel.sa',
        email: '<EMAIL>'
    },

    // Server Configuration
    server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || '127.0.0.1',
        environment: process.env.NODE_ENV || 'production'
    },

    // Database Configuration
    database: {
        path: process.env.DB_PATH || path.join(__dirname, '../data/app.db'),
        backupDir: process.env.BACKUP_DIR || path.join(__dirname, '../data/backups'),
        autoBackup: process.env.AUTO_BACKUP === 'true' || true,
        backupInterval: parseInt(process.env.BACKUP_INTERVAL) || 3600000, // 1 hour
        maxBackups: parseInt(process.env.MAX_BACKUPS) || 10
    },

    // Security Configuration
    security: {
        jwtSecret: process.env.JWT_SECRET || 'future-fuel-secret-key-2024',
        sessionSecret: process.env.SESSION_SECRET || 'session-secret-key',
        maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5,
        lockoutDuration: parseInt(process.env.LOCKOUT_DURATION) || 900000, // 15 minutes
        sessionTimeout: parseInt(process.env.SESSION_TIMEOUT) || 86400000, // 24 hours
        enableRateLimiting: process.env.ENABLE_RATE_LIMITING === 'true' || true,
        corsOrigins: process.env.ALLOW_ORIGINS ? 
            process.env.ALLOW_ORIGINS.split(',') : 
            ['http://localhost:3000', 'http://127.0.0.1:3000']
    },

    // License Configuration
    license: {
        defaultDuration: parseInt(process.env.DEFAULT_LICENSE_DURATION) || 365, // days
        maxActivations: parseInt(process.env.MAX_LICENSE_ACTIVATIONS) || 1,
        autoApproveRequests: process.env.AUTO_APPROVE_REQUESTS === 'true' || false,
        types: {
            trial: {
                name: 'تجريبي',
                nameEn: 'Trial',
                duration: 30, // days
                features: ['basic'],
                maxUsers: 1
            },
            premium: {
                name: 'مميز',
                nameEn: 'Premium',
                duration: 365, // days
                features: ['all'],
                maxUsers: 5
            },
            enterprise: {
                name: 'مؤسسي',
                nameEn: 'Enterprise',
                duration: null, // unlimited
                features: ['all', 'advanced'],
                maxUsers: -1 // unlimited
            }
        }
    },

    // Email Configuration
    email: {
        enabled: process.env.SMTP_HOST ? true : false,
        smtp: {
            host: process.env.SMTP_HOST,
            port: parseInt(process.env.SMTP_PORT) || 587,
            secure: false,
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            }
        },
        from: process.env.EMAIL_FROM || '<EMAIL>',
        templates: {
            licenseApproved: {
                subject: 'تم تفعيل ترخيصك - License Activated',
                template: 'license-approved'
            },
            licenseExpiring: {
                subject: 'تنبيه: ترخيصك قارب على الانتهاء - License Expiring Soon',
                template: 'license-expiring'
            }
        }
    },

    // Logging Configuration
    logging: {
        enabled: process.env.ENABLE_LOGGING === 'true' || true,
        level: process.env.LOG_LEVEL || 'info',
        file: path.join(__dirname, '../logs/app.log'),
        maxSize: '10m',
        maxFiles: 5
    },

    // File Upload Configuration
    upload: {
        maxSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        uploadDir: path.join(__dirname, '../uploads')
    },

    // API Configuration
    api: {
        version: 'v1',
        prefix: '/api',
        rateLimit: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100 // limit each IP to 100 requests per windowMs
        }
    },

    // Features Configuration
    features: {
        enableTelegramBackup: true,
        enableAutoBackup: true,
        enableAuditLog: true,
        enableNotifications: true,
        enableReports: true,
        enableAdvancedSecurity: true
    },

    // UI Configuration
    ui: {
        theme: {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#4CAF50',
            warning: '#ff9800',
            error: '#f44336',
            info: '#2196F3'
        },
        language: {
            default: 'ar',
            supported: ['ar', 'en']
        },
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm',
        currency: 'SAR'
    },

    // Development Configuration
    development: {
        debug: process.env.DEBUG === 'true' || false,
        hotReload: false,
        mockData: false
    }
};

// Validate required configuration
function validateConfig() {
    const config = module.exports;
    
    if (!config.security.jwtSecret || config.security.jwtSecret === 'future-fuel-secret-key-2024') {
        console.warn('⚠️  Warning: Using default JWT secret. Please change it in production!');
    }
    
    if (!config.security.sessionSecret || config.security.sessionSecret === 'session-secret-key') {
        console.warn('⚠️  Warning: Using default session secret. Please change it in production!');
    }
    
    return true;
}

// Export validation function
module.exports.validateConfig = validateConfig;
