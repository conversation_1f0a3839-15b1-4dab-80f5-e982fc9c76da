@echo off
chcp 65001 >nul
title إصلاح مشاكل تسجيل الدخول - نظام وقود المستقبل
color 0C

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  إصلاح مشاكل تسجيل الدخول                   ║
echo ║                   Login Issues Fix Tool                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 جاري إصلاح مشاكل تسجيل الدخول...
echo Fixing login issues...
echo.

:: Step 1: Check and install dependencies
echo [1/5] فحص وتثبيت التبعيات...
echo Checking and installing dependencies...

if not exist "node_modules" (
    echo تثبيت التبعيات...
    call npm install
    if errorlevel 1 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
) else (
    echo ✅ التبعيات مثبتة
)

echo.

:: Step 2: Create necessary directories
echo [2/5] إنشاء المجلدات المطلوبة...
echo Creating required directories...

if not exist "data" mkdir "data"
if not exist "logs" mkdir "logs"
if not exist "data\backups" mkdir "data\backups"

echo ✅ تم إنشاء المجلدات

echo.

:: Step 3: Reset database
echo [3/5] إعادة تعيين قاعدة البيانات...
echo Resetting database...

if exist "data\app.db" (
    echo حذف قاعدة البيانات القديمة...
    del "data\app.db"
)

echo ✅ تم إعادة تعيين قاعدة البيانات

echo.

:: Step 4: Initialize database with admin user
echo [4/5] تهيئة قاعدة البيانات وإنشاء المدير...
echo Initializing database and creating admin user...

node -e "
const { initializeDatabase } = require('./server/database/init');
initializeDatabase()
  .then(() => {
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    console.log('✅ Database initialized successfully');
    console.log('');
    console.log('🔐 بيانات المدير الافتراضية:');
    console.log('Default admin credentials:');
    console.log('   اسم المستخدم / Username: admin');
    console.log('   كلمة المرور / Password: admin123');
  })
  .catch(err => {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', err);
    process.exit(1);
  });
"

if errorlevel 1 (
    echo ❌ فشل في تهيئة قاعدة البيانات
    pause
    exit /b 1
)

echo.

:: Step 5: Test login functionality
echo [5/5] اختبار وظيفة تسجيل الدخول...
echo Testing login functionality...

echo جاري تشغيل الخادم للاختبار...
start /min cmd /c "node server.js"

timeout /t 3 >nul

echo اختبار API تسجيل الدخول...
powershell -Command "
try {
    $body = @{
        username = 'admin'
        password = 'admin123'
        machineId = 'TEST_MACHINE'
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri 'http://localhost:3000/api/auth/login' -Method Post -Body $body -ContentType 'application/json' -TimeoutSec 10
    
    if ($response.success) {
        Write-Host '✅ تسجيل الدخول يعمل بشكل صحيح' -ForegroundColor Green
        Write-Host '✅ Login functionality working correctly' -ForegroundColor Green
        exit 0
    } else {
        Write-Host '❌ فشل في تسجيل الدخول:' $response.message -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host '❌ خطأ في اختبار تسجيل الدخول:' $_.Exception.Message -ForegroundColor Red
    exit 1
}
"

set login_test_result=%errorlevel%

:: Stop test server
taskkill /f /im node.exe >nul 2>&1

echo.

if %login_test_result% equ 0 (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                    ✅ تم الإصلاح بنجاح!                     ║
    echo ║                  ✅ Fix Completed Successfully!              ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo 🎉 تم إصلاح جميع مشاكل تسجيل الدخول بنجاح!
    echo All login issues have been fixed successfully!
    echo.
    echo 🚀 يمكنك الآن تشغيل النظام:
    echo You can now start the system:
    echo.
    echo    1. تشغيل سريع: quick-start.bat
    echo       Quick start: quick-start.bat
    echo.
    echo    2. تشغيل تفاعلي: run.bat
    echo       Interactive start: run.bat
    echo.
    echo 🔐 بيانات تسجيل الدخول:
    echo Login credentials:
    echo    اسم المستخدم / Username: admin
    echo    كلمة المرور / Password: admin123
    echo.
    echo 🌐 الروابط:
    echo URLs:
    echo    التطبيق الرئيسي / Main App: http://localhost:3000
    echo    تسجيل الدخول / Login: http://localhost:3000/login.html
    echo    لوحة المطور / Admin Panel: http://localhost:3000/admin.html
    
) else (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                    ❌ فشل في الإصلاح                        ║
    echo ║                    ❌ Fix Failed                             ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo ❌ لم يتم إصلاح مشاكل تسجيل الدخول بالكامل
    echo Login issues were not completely fixed
    echo.
    echo 🔧 يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني:
    echo Please try again or contact technical support:
    echo    البريد الإلكتروني / Email: <EMAIL>
)

echo.
pause
