const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const config = require('../../config/app.config');
const logger = require('../../utils/logger');

// Database path
const DB_PATH = path.join(__dirname, '../../data/app.db');
const DATA_DIR = path.join(__dirname, '../../data');

let db = null;

// Initialize database
async function initializeDatabase() {
    return new Promise((resolve, reject) => {
        try {
            // Create data directory if it doesn't exist
            if (!fs.existsSync(DATA_DIR)) {
                fs.mkdirSync(DATA_DIR, { recursive: true });
            }

            // Create database connection
            db = new sqlite3.Database(DB_PATH, (err) => {
                if (err) {
                    console.error('خطأ في الاتصال بقاعدة البيانات:', err);
                    reject(err);
                    return;
                }
                console.log('✅ تم الاتصال بقاعدة البيانات');
            });

            // Enable foreign keys
            db.run('PRAGMA foreign_keys = ON');

            // Create tables
            createTables()
                .then(() => {
                    console.log('✅ تم إنشاء الجداول بنجاح');
                    resolve();
                })
                .catch(reject);

        } catch (error) {
            reject(error);
        }
    });
}

// Create database tables
function createTables() {
    return new Promise((resolve, reject) => {
        const tables = [
            // Users table
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                login_attempts INTEGER DEFAULT 0,
                locked_until DATETIME
            )`,

            // Licenses table
            `CREATE TABLE IF NOT EXISTS licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key TEXT UNIQUE NOT NULL,
                user_id INTEGER,
                machine_id TEXT,
                license_type TEXT DEFAULT 'trial',
                status TEXT DEFAULT 'pending',
                issued_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME,
                activated_at DATETIME,
                last_check DATETIME,
                features TEXT,
                max_users INTEGER DEFAULT 1,
                created_by INTEGER,
                notes TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )`,

            // License activations table
            `CREATE TABLE IF NOT EXISTS license_activations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                machine_id TEXT NOT NULL,
                machine_info TEXT,
                activated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_heartbeat DATETIME,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (license_id) REFERENCES licenses (id)
            )`,

            // Sessions table
            `CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                user_id INTEGER NOT NULL,
                license_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (license_id) REFERENCES licenses (id)
            )`,

            // Audit log table
            `CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                resource TEXT,
                resource_id TEXT,
                details TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // Settings table
            `CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        let completed = 0;
        const total = tables.length;

        tables.forEach((sql, index) => {
            db.run(sql, (err) => {
                if (err) {
                    console.error(`خطأ في إنشاء الجدول ${index + 1}:`, err);
                    reject(err);
                    return;
                }
                
                completed++;
                if (completed === total) {
                    // Create default admin user
                    createDefaultAdmin()
                        .then(() => resolve())
                        .catch(reject);
                }
            });
        });
    });
}

// Create default admin user
async function createDefaultAdmin() {
    const bcrypt = require('bcryptjs');
    
    return new Promise((resolve, reject) => {
        // Check if admin exists
        db.get('SELECT id FROM users WHERE username = ?', ['admin'], async (err, row) => {
            if (err) {
                reject(err);
                return;
            }

            if (!row) {
                try {
                    const hashedPassword = await bcrypt.hash('admin123', 10);
                    
                    db.run(`INSERT INTO users (username, password_hash, full_name, role, email) 
                            VALUES (?, ?, ?, ?, ?)`, 
                        ['admin', hashedPassword, 'مدير النظام', 'admin', '<EMAIL>'], 
                        (err) => {
                            if (err) {
                                reject(err);
                                return;
                            }
                            console.log('✅ تم إنشاء حساب المدير الافتراضي');
                            console.log('👤 اسم المستخدم: admin');
                            console.log('🔑 كلمة المرور: admin123');
                            resolve();
                        });
                } catch (error) {
                    reject(error);
                }
            } else {
                resolve();
            }
        });
    });
}

// Get database instance
function getDatabase() {
    return db;
}

// Close database connection
function closeDatabase() {
    if (db) {
        db.close((err) => {
            if (err) {
                console.error('خطأ في إغلاق قاعدة البيانات:', err);
            } else {
                console.log('✅ تم إغلاق قاعدة البيانات');
            }
        });
    }
}

module.exports = {
    initializeDatabase,
    getDatabase,
    closeDatabase
};
