{"name": "gas-shop-management", "productName": "مؤسسة وقود المستقبل", "version": "2.2.0", "description": "نظام إدارة مؤسسة وقود المستقبل - Future Fuel Corporation Management System", "main": "main.js", "homepage": "https://github.com/future-fuel/gas-shop-management", "author": {"name": "Future Fuel Corporation", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "web": "node server.js", "electron": "electron main.js", "build": "npm run web", "build-win": "electron-builder --win", "build-win32": "electron-builder --win --ia32", "build-win64": "electron-builder --win --x64", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "npm install --production", "install-deps": "npm install express cors compression helmet open nodemon", "setup": "npm install && echo Setup completed successfully!", "test": "echo Tests will be added soon && exit 0", "lint": "echo Linting will be added soon && exit 0", "clean": "echo Cleaning build files... && exit 0", "admin": "start http://localhost:3000/admin.html", "login": "start http://localhost:3000/login.html"}, "keywords": ["gas", "shop", "management", "arabic", "fuel", "certificates", "customers", "inventory"], "build": {"appId": "com.futurefuel.gasshop", "productName": "مؤسسة وقود المستقبل", "copyright": "Copyright © 2024 Future Fuel Corporation", "directories": {"output": "build-output"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}", "!dist/", "!build/", "!*.bat", "!*.vbs", "!*.ps1", "!*.nsh"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icons/app-icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "assets/icons/app-icon.ico", "uninstallerIcon": "assets/icons/app-icon.ico", "installerHeaderIcon": "assets/icons/app-icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "مؤسسة وقود المستقبل", "include": "installer.nsh", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Business", "artifactName": "${productName}-Setup-${version}-${arch}.${ext}", "displayLanguageSelector": false, "language": "ar"}, "portable": {"artifactName": "${productName}-Portable-${version}-${arch}.${ext}"}, "compression": "maximum", "npmRebuild": false, "nodeGypRebuild": false}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "compression": "^1.7.4", "helmet": "^7.1.0", "open": "^9.1.0", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "crypto-js": "^4.2.0", "node-machine-id": "^1.1.12", "axios": "^1.6.0", "nodemailer": "^6.9.7", "archiver": "^6.0.1", "yauzl": "^2.10.0"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "nodemon": "^3.0.2"}, "electronDownload": {"mirror": "https://github.com/electron/electron/releases/download/"}}