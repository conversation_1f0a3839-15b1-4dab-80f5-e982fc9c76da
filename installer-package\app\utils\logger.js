// Future Fuel Corporation - Logger Utility
// نظام إدارة مؤسسة وقود المستقبل - أداة السجلات

const fs = require('fs');
const path = require('path');
const config = require('../config/app.config');

class Logger {
    constructor() {
        this.logDir = path.dirname(config.logging.file);
        this.logFile = config.logging.file;
        this.enabled = config.logging.enabled;
        this.level = config.logging.level;
        
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        
        this.colors = {
            error: '\x1b[31m', // Red
            warn: '\x1b[33m',  // Yellow
            info: '\x1b[36m',  // Cyan
            debug: '\x1b[37m', // White
            reset: '\x1b[0m'
        };
        
        this.ensureLogDirectory();
    }

    ensureLogDirectory() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    shouldLog(level) {
        return this.enabled && this.levels[level] <= this.levels[this.level];
    }

    formatMessage(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
    }

    writeToFile(formattedMessage) {
        try {
            fs.appendFileSync(this.logFile, formattedMessage + '\n', 'utf8');
        } catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }

    writeToConsole(level, message, meta = {}) {
        const color = this.colors[level] || this.colors.reset;
        const timestamp = new Date().toLocaleString('ar-SA');
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta, null, 2)}` : '';
        
        console.log(`${color}[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}${this.colors.reset}`);
    }

    log(level, message, meta = {}) {
        if (!this.shouldLog(level)) return;

        const formattedMessage = this.formatMessage(level, message, meta);
        
        // Write to console
        this.writeToConsole(level, message, meta);
        
        // Write to file
        this.writeToFile(formattedMessage);
    }

    error(message, meta = {}) {
        this.log('error', message, meta);
    }

    warn(message, meta = {}) {
        this.log('warn', message, meta);
    }

    info(message, meta = {}) {
        this.log('info', message, meta);
    }

    debug(message, meta = {}) {
        this.log('debug', message, meta);
    }

    // Security logging methods
    security(action, details = {}) {
        this.info(`SECURITY: ${action}`, {
            type: 'security',
            action: action,
            ...details
        });
    }

    auth(action, user, details = {}) {
        this.info(`AUTH: ${action}`, {
            type: 'authentication',
            action: action,
            user: user,
            ...details
        });
    }

    license(action, licenseKey, details = {}) {
        this.info(`LICENSE: ${action}`, {
            type: 'license',
            action: action,
            licenseKey: licenseKey,
            ...details
        });
    }

    api(method, url, statusCode, responseTime, details = {}) {
        const level = statusCode >= 400 ? 'warn' : 'info';
        this.log(level, `API: ${method} ${url} ${statusCode} ${responseTime}ms`, {
            type: 'api',
            method: method,
            url: url,
            statusCode: statusCode,
            responseTime: responseTime,
            ...details
        });
    }

    database(action, details = {}) {
        this.debug(`DB: ${action}`, {
            type: 'database',
            action: action,
            ...details
        });
    }

    // Log rotation
    rotateLog() {
        try {
            if (!fs.existsSync(this.logFile)) return;

            const stats = fs.statSync(this.logFile);
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (stats.size > maxSize) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const rotatedFile = this.logFile.replace('.log', `_${timestamp}.log`);
                
                fs.renameSync(this.logFile, rotatedFile);
                this.info('Log file rotated', { rotatedFile: rotatedFile });
                
                // Keep only last 5 rotated files
                this.cleanOldLogs();
            }
        } catch (error) {
            console.error('Failed to rotate log file:', error);
        }
    }

    cleanOldLogs() {
        try {
            const files = fs.readdirSync(this.logDir)
                .filter(file => file.startsWith('app_') && file.endsWith('.log'))
                .map(file => ({
                    name: file,
                    path: path.join(this.logDir, file),
                    time: fs.statSync(path.join(this.logDir, file)).mtime
                }))
                .sort((a, b) => b.time - a.time);

            // Keep only the 5 most recent files
            const filesToDelete = files.slice(5);
            
            filesToDelete.forEach(file => {
                fs.unlinkSync(file.path);
                this.info('Old log file deleted', { file: file.name });
            });
        } catch (error) {
            console.error('Failed to clean old logs:', error);
        }
    }

    // Get log statistics
    getStats() {
        try {
            if (!fs.existsSync(this.logFile)) {
                return { size: 0, lines: 0, lastModified: null };
            }

            const stats = fs.statSync(this.logFile);
            const content = fs.readFileSync(this.logFile, 'utf8');
            const lines = content.split('\n').length - 1;

            return {
                size: stats.size,
                lines: lines,
                lastModified: stats.mtime,
                sizeFormatted: this.formatBytes(stats.size)
            };
        } catch (error) {
            this.error('Failed to get log stats', { error: error.message });
            return { size: 0, lines: 0, lastModified: null };
        }
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Export logs
    exportLogs(startDate, endDate) {
        try {
            if (!fs.existsSync(this.logFile)) {
                return null;
            }

            const content = fs.readFileSync(this.logFile, 'utf8');
            const lines = content.split('\n').filter(line => line.trim());

            if (!startDate && !endDate) {
                return lines;
            }

            const start = startDate ? new Date(startDate) : new Date(0);
            const end = endDate ? new Date(endDate) : new Date();

            return lines.filter(line => {
                const match = line.match(/\[(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z)\]/);
                if (!match) return false;

                const logDate = new Date(match[1]);
                return logDate >= start && logDate <= end;
            });
        } catch (error) {
            this.error('Failed to export logs', { error: error.message });
            return null;
        }
    }
}

// Create singleton instance
const logger = new Logger();

// Set up log rotation interval (every hour)
setInterval(() => {
    logger.rotateLog();
}, 60 * 60 * 1000);

module.exports = logger;
