// Login Page JavaScript - Future Fuel Corporation
// Version 2.2.0 - Authentication & License Management

class LoginManager {
    constructor() {
        this.apiBaseUrl = window.location.origin + '/api';
        this.machineId = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.checkConnection();
        await this.getMachineId();
        this.loadSavedCredentials();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // Form submissions
        document.getElementById('login-form').addEventListener('submit', (e) => this.handleLogin(e));
        document.getElementById('request-form').addEventListener('submit', (e) => this.handleLicenseRequest(e));

        // Connection check interval
        setInterval(() => this.checkConnection(), 30000); // Every 30 seconds
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    async getMachineId() {
        try {
            // Try to get machine ID from various sources
            let machineId = localStorage.getItem('machine_id');
            
            if (!machineId) {
                // Generate a unique machine ID based on browser fingerprint
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Machine ID Generator', 2, 2);
                
                const fingerprint = [
                    navigator.userAgent,
                    navigator.language,
                    screen.width + 'x' + screen.height,
                    new Date().getTimezoneOffset(),
                    canvas.toDataURL()
                ].join('|');
                
                machineId = 'WEB_' + this.hashString(fingerprint);
                localStorage.setItem('machine_id', machineId);
            }
            
            this.machineId = machineId;
            console.log('Machine ID:', this.machineId);
        } catch (error) {
            console.error('Error generating machine ID:', error);
            this.machineId = 'WEB_' + Math.random().toString(36).substr(2, 9);
        }
    }

    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36).toUpperCase();
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const loginData = {
            username: formData.get('username'),
            password: formData.get('password'),
            machineId: this.machineId,
            remember: formData.get('remember') === 'on'
        };

        if (!loginData.username || !loginData.password) {
            this.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(loginData)
            });

            const result = await response.json();

            if (result.success) {
                // Save authentication data
                localStorage.setItem('auth_token', result.data.token);
                localStorage.setItem('session_id', result.data.sessionId);
                localStorage.setItem('user_data', JSON.stringify(result.data.user));
                
                if (result.data.license) {
                    localStorage.setItem('license_data', JSON.stringify(result.data.license));
                }

                // Save credentials if remember me is checked
                if (loginData.remember) {
                    localStorage.setItem('saved_username', loginData.username);
                }

                this.showMessage('تم تسجيل الدخول بنجاح! جاري التحويل...', 'success');
                
                // Redirect to main application
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);

            } else {
                this.showMessage(result.message || 'خطأ في تسجيل الدخول', 'error');
                
                if (result.attemptsLeft !== undefined) {
                    this.showMessage(`المحاولات المتبقية: ${result.attemptsLeft}`, 'warning');
                }
            }

        } catch (error) {
            console.error('Login error:', error);
            this.showMessage('خطأ في الاتصال بالخادم', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async handleLicenseRequest(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const requestData = {
            username: formData.get('username'),
            email: formData.get('email'),
            full_name: formData.get('full_name'),
            company: formData.get('company'),
            phone: formData.get('phone'),
            notes: formData.get('notes'),
            machineId: this.machineId
        };

        // Validation
        if (!requestData.username || !requestData.email || !requestData.full_name) {
            this.showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        if (!this.isValidEmail(requestData.email)) {
            this.showMessage('يرجى إدخال بريد إلكتروني صحيح', 'error');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Sending license request to:', `${this.apiBaseUrl}/auth/request-license`);
            console.log('Request data:', requestData);

            const response = await fetch(`${this.apiBaseUrl}/auth/request-license`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('Response result:', result);

            if (result.success) {
                this.showModal('طلب الترخيص', `
                    <div style="text-align: center; padding: 20px;">
                        <i class="fas fa-check-circle" style="font-size: 48px; color: #4CAF50; margin-bottom: 20px;"></i>
                        <h3 style="color: #333; margin-bottom: 15px;">تم إرسال طلب الترخيص بنجاح!</h3>
                        <p style="margin-bottom: 20px;">معلومات الترخيص:</p>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <p><strong>مفتاح الترخيص:</strong> <code style="background: #e9ecef; padding: 4px 8px; border-radius: 4px;">${result.data.license_key}</code></p>
                            <p><strong>كلمة المرور المؤقتة:</strong> <code style="background: #e9ecef; padding: 4px 8px; border-radius: 4px;">${result.data.temp_password}</code></p>
                        </div>
                        <p style="color: #666; font-size: 14px;">
                            سيتم مراجعة طلبك وتفعيل الترخيص قريباً. 
                            احتفظ بمفتاح الترخيص وكلمة المرور المؤقتة في مكان آمن.
                        </p>
                    </div>
                `);

                // Reset form
                e.target.reset();

            } else {
                this.showMessage(result.message || 'خطأ في إرسال طلب الترخيص', 'error');
            }

        } catch (error) {
            console.error('License request error:', error);

            let errorMessage = 'خطأ في الاتصال بالخادم';

            if (error.message.includes('Failed to fetch')) {
                errorMessage = 'تعذر الاتصال بالخادم. تأكد من تشغيل الخادم على http://localhost:3000';
            } else if (error.message.includes('HTTP 400')) {
                errorMessage = 'بيانات غير صحيحة. تأكد من ملء جميع الحقول المطلوبة';
            } else if (error.message.includes('HTTP 409')) {
                errorMessage = 'اسم المستخدم أو البريد الإلكتروني مستخدم مسبقاً';
            } else if (error.message.includes('HTTP 500')) {
                errorMessage = 'خطأ في الخادم. يرجى المحاولة مرة أخرى';
            }

            this.showMessage(errorMessage, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async checkConnection() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`, {
                method: 'GET',
                timeout: 5000
            });
            
            const isConnected = response.ok;
            this.updateConnectionStatus(isConnected);
            
        } catch (error) {
            this.updateConnectionStatus(false);
        }
    }

    updateConnectionStatus(isConnected) {
        const statusElement = document.getElementById('connection-status');
        const icon = statusElement.querySelector('i');
        const text = statusElement.querySelector('span');
        
        if (isConnected) {
            statusElement.classList.remove('disconnected');
            icon.className = 'fas fa-wifi';
            text.textContent = 'متصل';
        } else {
            statusElement.classList.add('disconnected');
            icon.className = 'fas fa-wifi-slash';
            text.textContent = 'غير متصل';
        }
    }

    loadSavedCredentials() {
        const savedUsername = localStorage.getItem('saved_username');
        if (savedUsername) {
            document.getElementById('username').value = savedUsername;
            document.getElementById('remember-me').checked = true;
        }
    }

    showMessage(message, type = 'info') {
        const container = document.getElementById('message-container');
        const messageElement = document.getElementById('message');
        const icon = messageElement.querySelector('.message-icon');
        const text = messageElement.querySelector('.message-text');

        // Set icon based on type
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        icon.className = `message-icon ${icons[type] || icons.info}`;
        text.textContent = message;
        messageElement.className = `message ${type}`;
        
        container.classList.remove('hidden');

        // Auto hide after 5 seconds
        setTimeout(() => {
            container.classList.add('hidden');
        }, 5000);
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }

    showModal(title, content) {
        document.getElementById('modal-title').textContent = title;
        document.getElementById('modal-body').innerHTML = content;
        document.getElementById('modal-overlay').classList.remove('hidden');
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

// Utility functions
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleBtn = document.querySelector('.toggle-password i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleBtn.className = 'fas fa-eye';
    }
}

function closeModal() {
    document.getElementById('modal-overlay').classList.add('hidden');
}

function showAbout() {
    const aboutContent = `
        <div style="text-align: center;">
            <h4>نظام إدارة مؤسسة وقود المستقبل</h4>
            <p><strong>الإصدار:</strong> 2.2.0</p>
            <p><strong>تاريخ الإصدار:</strong> 27/05/2025</p>
            <br>
            <p>نظام شامل لإدارة محطات الوقود يشمل:</p>
            <ul style="text-align: right; margin: 20px 0;">
                <li>إدارة الزبائن والمركبات</li>
                <li>إدارة بطاقات الغاز</li>
                <li>جدولة المواعيد</li>
                <li>إدارة المخزون</li>
                <li>تتبع المبيعات والمشتريات</li>
                <li>إدارة الديون</li>
                <li>طباعة الشهادات</li>
            </ul>
            <p><strong>حقوق الطبع والنشر:</strong><br>© 2024 مؤسسة وقود المستقبل</p>
        </div>
    `;
    loginManager.showModal('حول البرنامج', aboutContent);
}

function showSupport() {
    const supportContent = `
        <div>
            <h4>الدعم الفني</h4>
            <p>للحصول على المساعدة والدعم الفني:</p>
            <br>
            <p><i class="fas fa-envelope"></i> <strong>البريد الإلكتروني:</strong><br>
            <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <br>
            <p><i class="fas fa-globe"></i> <strong>الموقع الإلكتروني:</strong><br>
            <a href="https://futurefuel.sa" target="_blank">https://futurefuel.sa</a></p>
            <br>
            <p><i class="fas fa-clock"></i> <strong>ساعات العمل:</strong><br>
            الأحد - الخميس: 8:00 ص - 6:00 م</p>
        </div>
    `;
    loginManager.showModal('الدعم الفني', supportContent);
}

function showPrivacy() {
    const privacyContent = `
        <div>
            <h4>سياسة الخصوصية</h4>
            <p>نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية.</p>
            <br>
            <h5>جمع البيانات:</h5>
            <ul>
                <li>نجمع فقط البيانات الضرورية لتشغيل النظام</li>
                <li>لا نشارك بياناتك مع أطراف ثالثة</li>
                <li>جميع البيانات محفوظة محلياً على جهازك</li>
            </ul>
            <br>
            <h5>الأمان:</h5>
            <ul>
                <li>جميع البيانات مشفرة</li>
                <li>نستخدم بروتوكولات أمان متقدمة</li>
                <li>يتم إنشاء نسخ احتياطية تلقائية</li>
            </ul>
        </div>
    `;
    loginManager.showModal('سياسة الخصوصية', privacyContent);
}

function showForgotPassword() {
    const forgotContent = `
        <div>
            <h4>استعادة كلمة المرور</h4>
            <p>لاستعادة كلمة المرور، يرجى التواصل مع الدعم الفني:</p>
            <br>
            <p><i class="fas fa-envelope"></i> البريد الإلكتروني: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <br>
            <p>يرجى تضمين المعلومات التالية في رسالتك:</p>
            <ul>
                <li>اسم المستخدم</li>
                <li>البريد الإلكتروني المسجل</li>
                <li>مفتاح الترخيص (إن وجد)</li>
            </ul>
        </div>
    `;
    loginManager.showModal('استعادة كلمة المرور', forgotContent);
}

// Initialize login manager when page loads
let loginManager;
document.addEventListener('DOMContentLoaded', () => {
    loginManager = new LoginManager();
});
