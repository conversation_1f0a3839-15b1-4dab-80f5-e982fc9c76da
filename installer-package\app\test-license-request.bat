@echo off
chcp 65001 >nul
title اختبار طلب التفعيل - نظام وقود المستقبل
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    اختبار طلب التفعيل                       ║
echo ║                License Request Test Tool                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 جاري اختبار وظيفة طلب التفعيل...
echo Testing license request functionality...
echo.

:: Step 1: Check if server is running
echo [1/3] فحص حالة الخادم...
echo Checking server status...

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/api/health' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ الخادم يعمل' -ForegroundColor Green; exit 0 } else { Write-Host '❌ الخادم لا يستجيب' -ForegroundColor Red; exit 1 } } catch { Write-Host '❌ الخادم غير متاح' -ForegroundColor Red; exit 1 }"

if errorlevel 1 (
    echo.
    echo ⚠️  الخادم غير متاح. جاري تشغيل الخادم...
    echo Server not available. Starting server...
    
    start /min cmd /c "node server.js"
    echo انتظار تشغيل الخادم...
    timeout /t 5 >nul
    
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/api/health' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ تم تشغيل الخادم' -ForegroundColor Green; exit 0 } else { Write-Host '❌ فشل في تشغيل الخادم' -ForegroundColor Red; exit 1 } } catch { Write-Host '❌ فشل في تشغيل الخادم' -ForegroundColor Red; exit 1 }"
    
    if errorlevel 1 (
        echo ❌ لا يمكن تشغيل الخادم. يرجى التحقق من المشاكل.
        pause
        exit /b 1
    )
)

echo.

:: Step 2: Test license request API
echo [2/3] اختبار API طلب التفعيل...
echo Testing license request API...

powershell -Command "
try {
    $body = @{
        username = 'test_user_' + (Get-Date -Format 'yyyyMMddHHmmss')
        email = '<EMAIL>'
        full_name = 'مستخدم تجريبي'
        company = 'شركة تجريبية'
        phone = '**********'
        notes = 'طلب تجريبي للاختبار'
        machineId = 'TEST_MACHINE_' + (Get-Date -Format 'yyyyMMddHHmmss')
    } | ConvertTo-Json
    
    Write-Host 'إرسال طلب التفعيل...' -ForegroundColor Yellow
    Write-Host 'Sending license request...' -ForegroundColor Yellow
    
    $response = Invoke-RestMethod -Uri 'http://localhost:3000/api/auth/request-license' -Method Post -Body $body -ContentType 'application/json' -TimeoutSec 15
    
    if ($response.success) {
        Write-Host '✅ تم إرسال طلب التفعيل بنجاح!' -ForegroundColor Green
        Write-Host '✅ License request sent successfully!' -ForegroundColor Green
        Write-Host ''
        Write-Host '📋 تفاصيل الطلب:' -ForegroundColor Cyan
        Write-Host 'Request details:' -ForegroundColor Cyan
        Write-Host ('   مفتاح الترخيص: ' + $response.data.license_key) -ForegroundColor White
        Write-Host ('   License Key: ' + $response.data.license_key) -ForegroundColor White
        Write-Host ('   كلمة المرور المؤقتة: ' + $response.data.temp_password) -ForegroundColor White
        Write-Host ('   Temporary Password: ' + $response.data.temp_password) -ForegroundColor White
        exit 0
    } else {
        Write-Host ('❌ فشل في إرسال الطلب: ' + $response.message) -ForegroundColor Red
        Write-Host ('❌ Request failed: ' + $response.message) -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host ('❌ خطأ في الاتصال: ' + $_.Exception.Message) -ForegroundColor Red
    Write-Host ('❌ Connection error: ' + $_.Exception.Message) -ForegroundColor Red
    
    if ($_.Exception.Message -like '*Failed to connect*') {
        Write-Host 'تأكد من تشغيل الخادم على http://localhost:3000' -ForegroundColor Yellow
        Write-Host 'Make sure server is running on http://localhost:3000' -ForegroundColor Yellow
    }
    
    exit 1
}
"

set api_test_result=%errorlevel%

echo.

:: Step 3: Test web interface
echo [3/3] اختبار واجهة الويب...
echo Testing web interface...

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/login.html' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ صفحة تسجيل الدخول متاحة' -ForegroundColor Green; exit 0 } else { Write-Host '❌ صفحة تسجيل الدخول غير متاحة' -ForegroundColor Red; exit 1 } } catch { Write-Host '❌ خطأ في الوصول لصفحة تسجيل الدخول' -ForegroundColor Red; exit 1 }"

set web_test_result=%errorlevel%

echo.

:: Results summary
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      نتائج الاختبار                         ║
echo ║                     Test Results                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

if %api_test_result% equ 0 (
    echo ✅ API طلب التفعيل يعمل بشكل صحيح
    echo ✅ License request API working correctly
) else (
    echo ❌ مشكلة في API طلب التفعيل
    echo ❌ Issue with license request API
)

if %web_test_result% equ 0 (
    echo ✅ واجهة الويب متاحة
    echo ✅ Web interface available
) else (
    echo ❌ مشكلة في واجهة الويب
    echo ❌ Issue with web interface
)

echo.

if %api_test_result% equ 0 (
    echo 🎉 طلب التفعيل يعمل بشكل صحيح!
    echo 🎉 License request is working correctly!
    echo.
    echo 🌐 يمكنك الآن اختبار طلب التفعيل من المتصفح:
    echo 🌐 You can now test license request from browser:
    echo    http://localhost:3000/login.html
    echo.
    echo 📝 انقر على تبويب "طلب ترخيص" وأملأ النموذج
    echo 📝 Click on "Request License" tab and fill the form
    
    set /p open_browser="هل تريد فتح صفحة تسجيل الدخول؟ (y/n): "
    if /i "%open_browser%"=="y" (
        start http://localhost:3000/login.html
    )
    
) else (
    echo ❌ هناك مشاكل في طلب التفعيل
    echo ❌ There are issues with license request
    echo.
    echo 🔧 يرجى تشغيل أداة الإصلاح:
    echo 🔧 Please run the fix tool:
    echo    fix-login.bat
)

echo.
pause
