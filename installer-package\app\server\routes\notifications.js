const express = require('express');
const { getDatabase } = require('../database/init');
const { authenticateToken, requireRole, auditLog } = require('../middleware/auth');
const notifications = require('../../utils/notifications');

const router = express.Router();

// Get user notifications
router.get('/', authenticateToken, auditLog('get_notifications'), async (req, res) => {
    try {
        const { limit = 50, offset = 0, unreadOnly = false, type } = req.query;
        
        const userNotifications = await notifications.getUserNotifications(req.user.id, {
            limit: parseInt(limit),
            offset: parseInt(offset),
            unreadOnly: unreadOnly === 'true',
            type: type
        });

        res.json({
            success: true,
            data: userNotifications
        });

    } catch (error) {
        console.error('Get notifications error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الإشعارات'
        });
    }
});

// Get unread count
router.get('/unread-count', authenticateToken, async (req, res) => {
    try {
        const count = await notifications.getUnreadCount(req.user.id);
        
        res.json({
            success: true,
            count: count
        });

    } catch (error) {
        console.error('Get unread count error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب عدد الإشعارات'
        });
    }
});

// Mark notification as read
router.post('/:id/read', authenticateToken, auditLog('mark_notification_read'), async (req, res) => {
    try {
        const { id } = req.params;
        
        const success = await notifications.markAsRead(id, req.user.id);
        
        if (success) {
            res.json({
                success: true,
                message: 'تم تحديد الإشعار كمقروء'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'الإشعار غير موجود'
            });
        }

    } catch (error) {
        console.error('Mark notification read error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث الإشعار'
        });
    }
});

// Mark all notifications as read
router.post('/mark-all-read', authenticateToken, auditLog('mark_all_notifications_read'), async (req, res) => {
    try {
        const count = await notifications.markAllAsRead(req.user.id);
        
        res.json({
            success: true,
            message: `تم تحديد ${count} إشعار كمقروء`,
            count: count
        });

    } catch (error) {
        console.error('Mark all notifications read error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث الإشعارات'
        });
    }
});

// Delete notification
router.delete('/:id', authenticateToken, auditLog('delete_notification'), async (req, res) => {
    try {
        const { id } = req.params;
        
        const success = await notifications.deleteNotification(id, req.user.id);
        
        if (success) {
            res.json({
                success: true,
                message: 'تم حذف الإشعار'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'الإشعار غير موجود'
            });
        }

    } catch (error) {
        console.error('Delete notification error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف الإشعار'
        });
    }
});

// Create notification (admin only)
router.post('/', authenticateToken, requireRole(['admin']), auditLog('create_notification'), async (req, res) => {
    try {
        const { type, title, message, userId, data, priority, sendEmail } = req.body;
        
        if (!title || !message) {
            return res.status(400).json({
                success: false,
                message: 'العنوان والرسالة مطلوبان'
            });
        }

        const notification = await notifications.createNotification({
            type: type || 'info',
            title: title,
            message: message,
            userId: userId,
            data: data || {},
            priority: priority || 'normal',
            sendEmail: sendEmail || false
        });

        res.json({
            success: true,
            message: 'تم إنشاء الإشعار بنجاح',
            data: notification
        });

    } catch (error) {
        console.error('Create notification error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء الإشعار'
        });
    }
});

// Send system notification to all users (admin only)
router.post('/broadcast', authenticateToken, requireRole(['admin']), auditLog('broadcast_notification'), async (req, res) => {
    try {
        const { type, title, message, data, priority } = req.body;
        
        if (!title || !message) {
            return res.status(400).json({
                success: false,
                message: 'العنوان والرسالة مطلوبان'
            });
        }

        const notification = await notifications.createNotification({
            type: type || 'info',
            title: title,
            message: message,
            userId: null, // Global notification
            data: data || {},
            priority: priority || 'normal'
        });

        res.json({
            success: true,
            message: 'تم إرسال الإشعار لجميع المستخدمين',
            data: notification
        });

    } catch (error) {
        console.error('Broadcast notification error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إرسال الإشعار'
        });
    }
});

module.exports = router;
