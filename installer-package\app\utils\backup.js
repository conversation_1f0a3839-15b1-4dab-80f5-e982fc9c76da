// Future Fuel Corporation - Backup Utility
// نظام إدارة مؤسسة وقود المستقبل - أداة النسخ الاحتياطية

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const config = require('../config/app.config');
const logger = require('./logger');

class BackupManager {
    constructor() {
        this.backupDir = config.database.backupDir;
        this.dataFile = path.join(__dirname, '../data/app_data.json');
        this.dbFile = config.database.path;
        this.maxBackups = config.database.maxBackups;
        this.autoBackup = config.database.autoBackup;
        this.backupInterval = config.database.backupInterval;
        
        this.ensureBackupDirectory();
        
        if (this.autoBackup) {
            this.startAutoBackup();
        }
    }

    ensureBackupDirectory() {
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
            logger.info('Backup directory created', { path: this.backupDir });
        }
    }

    // Create full backup (database + data files)
    async createFullBackup(description = 'Auto backup') {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupName = `full_backup_${timestamp}`;
            const backupPath = path.join(this.backupDir, backupName);
            
            // Create backup directory
            fs.mkdirSync(backupPath, { recursive: true });
            
            const manifest = {
                type: 'full',
                timestamp: new Date().toISOString(),
                description: description,
                version: config.app.version,
                files: []
            };

            // Backup database
            if (fs.existsSync(this.dbFile)) {
                const dbBackupPath = path.join(backupPath, 'database.db');
                fs.copyFileSync(this.dbFile, dbBackupPath);
                manifest.files.push({
                    original: this.dbFile,
                    backup: 'database.db',
                    size: fs.statSync(this.dbFile).size,
                    checksum: this.calculateChecksum(this.dbFile)
                });
                logger.info('Database backed up', { path: dbBackupPath });
            }

            // Backup data files
            if (fs.existsSync(this.dataFile)) {
                const dataBackupPath = path.join(backupPath, 'app_data.json');
                fs.copyFileSync(this.dataFile, dataBackupPath);
                manifest.files.push({
                    original: this.dataFile,
                    backup: 'app_data.json',
                    size: fs.statSync(this.dataFile).size,
                    checksum: this.calculateChecksum(this.dataFile)
                });
                logger.info('Data file backed up', { path: dataBackupPath });
            }

            // Backup configuration (without sensitive data)
            const configBackup = this.sanitizeConfig(config);
            const configPath = path.join(backupPath, 'config.json');
            fs.writeFileSync(configPath, JSON.stringify(configBackup, null, 2));
            manifest.files.push({
                original: 'config',
                backup: 'config.json',
                size: fs.statSync(configPath).size,
                checksum: this.calculateChecksum(configPath)
            });

            // Save manifest
            const manifestPath = path.join(backupPath, 'manifest.json');
            fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));

            // Create compressed archive
            const archivePath = await this.createArchive(backupPath, `${backupName}.zip`);
            
            // Remove temporary directory
            this.removeDirectory(backupPath);

            // Clean old backups
            this.cleanOldBackups();

            logger.info('Full backup created successfully', {
                archive: archivePath,
                files: manifest.files.length,
                size: fs.statSync(archivePath).size
            });

            return {
                success: true,
                path: archivePath,
                manifest: manifest
            };

        } catch (error) {
            logger.error('Failed to create full backup', { error: error.message });
            throw error;
        }
    }

    // Create data-only backup
    async createDataBackup(description = 'Data backup') {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupName = `data_backup_${timestamp}.json`;
            const backupPath = path.join(this.backupDir, backupName);

            if (!fs.existsSync(this.dataFile)) {
                throw new Error('Data file not found');
            }

            // Read and backup data
            const data = JSON.parse(fs.readFileSync(this.dataFile, 'utf8'));
            
            const backupData = {
                metadata: {
                    type: 'data',
                    timestamp: new Date().toISOString(),
                    description: description,
                    version: config.app.version,
                    checksum: this.calculateChecksum(this.dataFile)
                },
                data: data
            };

            fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));

            logger.info('Data backup created successfully', {
                path: backupPath,
                size: fs.statSync(backupPath).size
            });

            return {
                success: true,
                path: backupPath,
                metadata: backupData.metadata
            };

        } catch (error) {
            logger.error('Failed to create data backup', { error: error.message });
            throw error;
        }
    }

    // Restore from backup
    async restoreBackup(backupPath, type = 'auto') {
        try {
            if (!fs.existsSync(backupPath)) {
                throw new Error('Backup file not found');
            }

            logger.info('Starting backup restoration', { path: backupPath, type: type });

            if (backupPath.endsWith('.zip')) {
                return await this.restoreFromArchive(backupPath);
            } else if (backupPath.endsWith('.json')) {
                return await this.restoreFromDataBackup(backupPath);
            } else {
                throw new Error('Unsupported backup format');
            }

        } catch (error) {
            logger.error('Failed to restore backup', { error: error.message, path: backupPath });
            throw error;
        }
    }

    // Restore from compressed archive
    async restoreFromArchive(archivePath) {
        const extractPath = path.join(this.backupDir, 'temp_restore');
        
        try {
            // Extract archive
            await this.extractArchive(archivePath, extractPath);

            // Read manifest
            const manifestPath = path.join(extractPath, 'manifest.json');
            if (!fs.existsSync(manifestPath)) {
                throw new Error('Invalid backup: manifest not found');
            }

            const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

            // Create backup of current state
            await this.createFullBackup('Pre-restore backup');

            // Restore files
            for (const file of manifest.files) {
                const sourcePath = path.join(extractPath, file.backup);
                const targetPath = file.original === 'config' ? null : file.original;

                if (targetPath && fs.existsSync(sourcePath)) {
                    // Verify checksum
                    const currentChecksum = this.calculateChecksum(sourcePath);
                    if (currentChecksum !== file.checksum) {
                        logger.warn('Checksum mismatch for file', { file: file.backup });
                    }

                    // Ensure target directory exists
                    const targetDir = path.dirname(targetPath);
                    if (!fs.existsSync(targetDir)) {
                        fs.mkdirSync(targetDir, { recursive: true });
                    }

                    // Restore file
                    fs.copyFileSync(sourcePath, targetPath);
                    logger.info('File restored', { from: sourcePath, to: targetPath });
                }
            }

            // Clean up
            this.removeDirectory(extractPath);

            logger.info('Backup restored successfully', { manifest: manifest });

            return {
                success: true,
                manifest: manifest,
                restoredFiles: manifest.files.length
            };

        } catch (error) {
            // Clean up on error
            if (fs.existsSync(extractPath)) {
                this.removeDirectory(extractPath);
            }
            throw error;
        }
    }

    // Restore from data backup
    async restoreFromDataBackup(backupPath) {
        try {
            const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf8'));

            if (backupData.metadata.type !== 'data') {
                throw new Error('Invalid data backup format');
            }

            // Create backup of current data
            if (fs.existsSync(this.dataFile)) {
                await this.createDataBackup('Pre-restore data backup');
            }

            // Restore data
            fs.writeFileSync(this.dataFile, JSON.stringify(backupData.data, null, 2));

            logger.info('Data restored successfully', {
                timestamp: backupData.metadata.timestamp,
                version: backupData.metadata.version
            });

            return {
                success: true,
                metadata: backupData.metadata
            };

        } catch (error) {
            throw error;
        }
    }

    // List available backups
    listBackups() {
        try {
            const files = fs.readdirSync(this.backupDir)
                .filter(file => file.endsWith('.zip') || file.endsWith('.json'))
                .map(file => {
                    const filePath = path.join(this.backupDir, file);
                    const stats = fs.statSync(filePath);
                    
                    return {
                        name: file,
                        path: filePath,
                        size: stats.size,
                        sizeFormatted: this.formatBytes(stats.size),
                        created: stats.birthtime,
                        modified: stats.mtime,
                        type: file.endsWith('.zip') ? 'full' : 'data'
                    };
                })
                .sort((a, b) => b.created - a.created);

            return files;

        } catch (error) {
            logger.error('Failed to list backups', { error: error.message });
            return [];
        }
    }

    // Delete backup
    deleteBackup(backupPath) {
        try {
            if (fs.existsSync(backupPath)) {
                fs.unlinkSync(backupPath);
                logger.info('Backup deleted', { path: backupPath });
                return true;
            }
            return false;
        } catch (error) {
            logger.error('Failed to delete backup', { error: error.message, path: backupPath });
            throw error;
        }
    }

    // Clean old backups
    cleanOldBackups() {
        try {
            const backups = this.listBackups();
            
            if (backups.length > this.maxBackups) {
                const toDelete = backups.slice(this.maxBackups);
                
                toDelete.forEach(backup => {
                    this.deleteBackup(backup.path);
                });

                logger.info('Old backups cleaned', { deleted: toDelete.length });
            }
        } catch (error) {
            logger.error('Failed to clean old backups', { error: error.message });
        }
    }

    // Start automatic backup
    startAutoBackup() {
        setInterval(async () => {
            try {
                await this.createFullBackup('Automatic backup');
            } catch (error) {
                logger.error('Auto backup failed', { error: error.message });
            }
        }, this.backupInterval);

        logger.info('Auto backup started', { interval: this.backupInterval });
    }

    // Utility methods
    calculateChecksum(filePath) {
        const fileBuffer = fs.readFileSync(filePath);
        return crypto.createHash('md5').update(fileBuffer).digest('hex');
    }

    sanitizeConfig(config) {
        const sanitized = JSON.parse(JSON.stringify(config));
        
        // Remove sensitive data
        if (sanitized.security) {
            delete sanitized.security.jwtSecret;
            delete sanitized.security.sessionSecret;
        }
        
        if (sanitized.email && sanitized.email.smtp) {
            delete sanitized.email.smtp.auth;
        }

        return sanitized;
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    removeDirectory(dirPath) {
        if (fs.existsSync(dirPath)) {
            fs.rmSync(dirPath, { recursive: true, force: true });
        }
    }

    // Archive methods (simplified - would need additional libraries for full implementation)
    async createArchive(sourcePath, archiveName) {
        // This is a simplified implementation
        // In production, you would use libraries like 'archiver' or 'node-7z'
        const archivePath = path.join(this.backupDir, archiveName);
        
        // For now, just copy the directory
        // TODO: Implement proper ZIP compression
        fs.copyFileSync(path.join(sourcePath, 'manifest.json'), archivePath.replace('.zip', '.json'));
        
        return archivePath.replace('.zip', '.json');
    }

    async extractArchive(archivePath, extractPath) {
        // This is a simplified implementation
        // In production, you would use libraries like 'yauzl' or 'node-7z'
        
        // For now, just copy the file
        // TODO: Implement proper ZIP extraction
        const manifestPath = path.join(extractPath, 'manifest.json');
        fs.mkdirSync(extractPath, { recursive: true });
        fs.copyFileSync(archivePath, manifestPath);
    }
}

module.exports = new BackupManager();
