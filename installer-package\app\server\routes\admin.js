const express = require('express');
const { getDatabase } = require('../database/init');
const { authenticateToken, requireRole, auditLog } = require('../middleware/auth');
const scheduler = require('../../utils/scheduler');
const backup = require('../../utils/backup');
const logger = require('../../utils/logger');

const router = express.Router();

// Get system statistics
router.get('/stats', authenticateToken, requireRole(['admin']), async (req, res) => {
    try {
        const db = getDatabase();
        
        // Get various statistics
        const stats = await Promise.all([
            new Promise((resolve) => {
                db.get('SELECT COUNT(*) as total FROM users', (err, row) => {
                    resolve(err ? 0 : row.total);
                });
            }),
            new Promise((resolve) => {
                db.get('SELECT COUNT(*) as total FROM licenses', (err, row) => {
                    resolve(err ? 0 : row.total);
                });
            }),
            new Promise((resolve) => {
                db.get('SELECT COUNT(*) as total FROM sessions WHERE is_active = 1', (err, row) => {
                    resolve(err ? 0 : row.total);
                });
            }),
            new Promise((resolve) => {
                db.get('SELECT COUNT(*) as total FROM audit_log WHERE created_at > datetime("now", "-24 hours")', (err, row) => {
                    resolve(err ? 0 : row.total);
                });
            })
        ]);

        // Get log statistics
        const logStats = logger.getStats();

        res.json({
            success: true,
            data: {
                users: stats[0],
                licenses: stats[1],
                activeSessions: stats[2],
                recentActivity: stats[3],
                logs: logStats,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                version: require('../../package.json').version
            }
        });

    } catch (error) {
        console.error('Get admin stats error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الإحصائيات'
        });
    }
});

// Get scheduled tasks status
router.get('/tasks', authenticateToken, requireRole(['admin']), (req, res) => {
    try {
        const tasks = scheduler.getAllTasksStatus();
        
        res.json({
            success: true,
            data: tasks
        });

    } catch (error) {
        console.error('Get tasks error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المهام'
        });
    }
});

// Control scheduled tasks
router.post('/tasks/:taskId/:action', authenticateToken, requireRole(['admin']), auditLog('task_control'), async (req, res) => {
    try {
        const { taskId, action } = req.params;
        
        switch (action) {
            case 'enable':
                scheduler.enableTask(taskId);
                break;
            case 'disable':
                scheduler.disableTask(taskId);
                break;
            case 'run':
                await scheduler.runTaskNow(taskId);
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: 'إجراء غير صالح'
                });
        }

        res.json({
            success: true,
            message: `تم ${action === 'enable' ? 'تفعيل' : action === 'disable' ? 'تعطيل' : 'تشغيل'} المهمة بنجاح`
        });

    } catch (error) {
        console.error('Task control error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في التحكم بالمهمة'
        });
    }
});

// Get backup list
router.get('/backups', authenticateToken, requireRole(['admin']), (req, res) => {
    try {
        const backups = backup.listBackups();
        
        res.json({
            success: true,
            data: backups
        });

    } catch (error) {
        console.error('Get backups error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب النسخ الاحتياطية'
        });
    }
});

// Create backup
router.post('/backups', authenticateToken, requireRole(['admin']), auditLog('create_backup'), async (req, res) => {
    try {
        const { type = 'full', description = 'Manual backup' } = req.body;
        
        let result;
        if (type === 'full') {
            result = await backup.createFullBackup(description);
        } else {
            result = await backup.createDataBackup(description);
        }

        res.json({
            success: true,
            message: 'تم إنشاء النسخة الاحتياطية بنجاح',
            data: result
        });

    } catch (error) {
        console.error('Create backup error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء النسخة الاحتياطية'
        });
    }
});

// Restore backup
router.post('/backups/:filename/restore', authenticateToken, requireRole(['admin']), auditLog('restore_backup'), async (req, res) => {
    try {
        const { filename } = req.params;
        const backupPath = require('path').join(backup.backupDir, filename);
        
        const result = await backup.restoreBackup(backupPath);

        res.json({
            success: true,
            message: 'تم استعادة النسخة الاحتياطية بنجاح',
            data: result
        });

    } catch (error) {
        console.error('Restore backup error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في استعادة النسخة الاحتياطية'
        });
    }
});

// Delete backup
router.delete('/backups/:filename', authenticateToken, requireRole(['admin']), auditLog('delete_backup'), (req, res) => {
    try {
        const { filename } = req.params;
        const backupPath = require('path').join(backup.backupDir, filename);
        
        const success = backup.deleteBackup(backupPath);

        if (success) {
            res.json({
                success: true,
                message: 'تم حذف النسخة الاحتياطية'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'النسخة الاحتياطية غير موجودة'
            });
        }

    } catch (error) {
        console.error('Delete backup error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف النسخة الاحتياطية'
        });
    }
});

// Get audit logs
router.get('/audit-logs', authenticateToken, requireRole(['admin']), (req, res) => {
    try {
        const { limit = 100, offset = 0, action, userId, startDate, endDate } = req.query;
        const db = getDatabase();

        let query = 'SELECT * FROM audit_log WHERE 1=1';
        const params = [];

        if (action) {
            query += ' AND action = ?';
            params.push(action);
        }

        if (userId) {
            query += ' AND user_id = ?';
            params.push(userId);
        }

        if (startDate) {
            query += ' AND created_at >= ?';
            params.push(startDate);
        }

        if (endDate) {
            query += ' AND created_at <= ?';
            params.push(endDate);
        }

        query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), parseInt(offset));

        db.all(query, params, (err, logs) => {
            if (err) {
                console.error('Get audit logs error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'خطأ في جلب سجل العمليات'
                });
            }

            res.json({
                success: true,
                data: logs.map(log => ({
                    ...log,
                    details: log.details ? JSON.parse(log.details) : null
                }))
            });
        });

    } catch (error) {
        console.error('Get audit logs error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب سجل العمليات'
        });
    }
});

// Get system logs
router.get('/logs', authenticateToken, requireRole(['admin']), (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        const logs = logger.exportLogs(startDate, endDate);
        
        if (logs) {
            res.json({
                success: true,
                data: logs
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'لا توجد سجلات متاحة'
            });
        }

    } catch (error) {
        console.error('Get logs error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب السجلات'
        });
    }
});

// System maintenance
router.post('/maintenance', authenticateToken, requireRole(['admin']), auditLog('system_maintenance'), async (req, res) => {
    try {
        const { action } = req.body;
        const db = getDatabase();

        switch (action) {
            case 'vacuum':
                db.run('VACUUM', (err) => {
                    if (err) {
                        return res.status(500).json({
                            success: false,
                            message: 'خطأ في تحسين قاعدة البيانات'
                        });
                    }
                    res.json({
                        success: true,
                        message: 'تم تحسين قاعدة البيانات بنجاح'
                    });
                });
                break;

            case 'cleanup':
                // Clean old sessions
                db.run('UPDATE sessions SET is_active = 0 WHERE expires_at < CURRENT_TIMESTAMP', (err) => {
                    if (err) {
                        return res.status(500).json({
                            success: false,
                            message: 'خطأ في تنظيف الجلسات'
                        });
                    }
                    res.json({
                        success: true,
                        message: 'تم تنظيف الجلسات المنتهية'
                    });
                });
                break;

            default:
                res.status(400).json({
                    success: false,
                    message: 'إجراء صيانة غير صالح'
                });
        }

    } catch (error) {
        console.error('System maintenance error:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في صيانة النظام'
        });
    }
});

module.exports = router;
