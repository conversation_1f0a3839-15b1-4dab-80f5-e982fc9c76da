// Future Fuel Corporation - Notifications Utility
// نظام إدارة مؤسسة وقود المستقبل - أداة الإشعارات

const { getDatabase } = require('../server/database/init');
const logger = require('./logger');
const config = require('../config/app.config');

// Initialize notifications table (already done in database init)
async function initializeTable() {
    // Table is already created in database/init.js
    return Promise.resolve();
}

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.subscribers = new Map();
        this.emailEnabled = config.email.enabled;
        
        // Start notification cleanup interval
        setInterval(() => {
            this.cleanupOldNotifications();
        }, 60 * 60 * 1000); // Every hour
    }

    // Create notification
    async createNotification(data) {
        try {
            const notification = {
                id: this.generateId(),
                type: data.type || 'info',
                title: data.title,
                message: data.message,
                userId: data.userId || null,
                data: data.data || {},
                read: false,
                created_at: new Date().toISOString(),
                expires_at: data.expires_at || null,
                priority: data.priority || 'normal'
            };

            // Store in memory
            this.notifications.push(notification);

            // Store in database if user-specific
            if (notification.userId) {
                await this.saveToDatabase(notification);
            }

            // Send to subscribers
            this.notifySubscribers(notification);

            // Send email if enabled and required
            if (this.emailEnabled && data.sendEmail) {
                await this.sendEmailNotification(notification);
            }

            logger.info('Notification created', {
                id: notification.id,
                type: notification.type,
                userId: notification.userId
            });

            return notification;

        } catch (error) {
            logger.error('Failed to create notification', { error: error.message });
            throw error;
        }
    }

    // Get notifications for user
    async getUserNotifications(userId, options = {}) {
        try {
            const {
                limit = 50,
                offset = 0,
                unreadOnly = false,
                type = null
            } = options;

            const db = getDatabase();
            
            let query = `
                SELECT * FROM notifications 
                WHERE user_id = ? OR user_id IS NULL
            `;
            
            const params = [userId];

            if (unreadOnly) {
                query += ' AND read = 0';
            }

            if (type) {
                query += ' AND type = ?';
                params.push(type);
            }

            query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            return new Promise((resolve, reject) => {
                db.all(query, params, (err, rows) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    const notifications = rows.map(row => ({
                        ...row,
                        data: row.data ? JSON.parse(row.data) : {}
                    }));

                    resolve(notifications);
                });
            });

        } catch (error) {
            logger.error('Failed to get user notifications', { error: error.message, userId });
            throw error;
        }
    }

    // Mark notification as read
    async markAsRead(notificationId, userId) {
        try {
            const db = getDatabase();

            return new Promise((resolve, reject) => {
                db.run(
                    'UPDATE notifications SET read = 1 WHERE id = ? AND user_id = ?',
                    [notificationId, userId],
                    function(err) {
                        if (err) {
                            reject(err);
                            return;
                        }

                        logger.info('Notification marked as read', {
                            id: notificationId,
                            userId: userId
                        });

                        resolve(this.changes > 0);
                    }
                );
            });

        } catch (error) {
            logger.error('Failed to mark notification as read', { error: error.message });
            throw error;
        }
    }

    // Mark all notifications as read for user
    async markAllAsRead(userId) {
        try {
            const db = getDatabase();

            return new Promise((resolve, reject) => {
                db.run(
                    'UPDATE notifications SET read = 1 WHERE user_id = ? AND read = 0',
                    [userId],
                    function(err) {
                        if (err) {
                            reject(err);
                            return;
                        }

                        logger.info('All notifications marked as read', {
                            userId: userId,
                            count: this.changes
                        });

                        resolve(this.changes);
                    }
                );
            });

        } catch (error) {
            logger.error('Failed to mark all notifications as read', { error: error.message });
            throw error;
        }
    }

    // Delete notification
    async deleteNotification(notificationId, userId) {
        try {
            const db = getDatabase();

            return new Promise((resolve, reject) => {
                db.run(
                    'DELETE FROM notifications WHERE id = ? AND user_id = ?',
                    [notificationId, userId],
                    function(err) {
                        if (err) {
                            reject(err);
                            return;
                        }

                        logger.info('Notification deleted', {
                            id: notificationId,
                            userId: userId
                        });

                        resolve(this.changes > 0);
                    }
                );
            });

        } catch (error) {
            logger.error('Failed to delete notification', { error: error.message });
            throw error;
        }
    }

    // Get unread count for user
    async getUnreadCount(userId) {
        try {
            const db = getDatabase();

            return new Promise((resolve, reject) => {
                db.get(
                    'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND read = 0',
                    [userId],
                    (err, row) => {
                        if (err) {
                            reject(err);
                            return;
                        }

                        resolve(row.count);
                    }
                );
            });

        } catch (error) {
            logger.error('Failed to get unread count', { error: error.message });
            throw error;
        }
    }

    // Subscribe to real-time notifications
    subscribe(userId, callback) {
        if (!this.subscribers.has(userId)) {
            this.subscribers.set(userId, []);
        }
        
        this.subscribers.get(userId).push(callback);

        logger.debug('User subscribed to notifications', { userId });

        // Return unsubscribe function
        return () => {
            const callbacks = this.subscribers.get(userId);
            if (callbacks) {
                const index = callbacks.indexOf(callback);
                if (index > -1) {
                    callbacks.splice(index, 1);
                }
                
                if (callbacks.length === 0) {
                    this.subscribers.delete(userId);
                }
            }
        };
    }

    // Notify subscribers
    notifySubscribers(notification) {
        try {
            // Notify specific user
            if (notification.userId) {
                const callbacks = this.subscribers.get(notification.userId);
                if (callbacks) {
                    callbacks.forEach(callback => {
                        try {
                            callback(notification);
                        } catch (error) {
                            logger.error('Notification callback error', { error: error.message });
                        }
                    });
                }
            }

            // Notify all subscribers for global notifications
            if (!notification.userId) {
                this.subscribers.forEach((callbacks, userId) => {
                    callbacks.forEach(callback => {
                        try {
                            callback(notification);
                        } catch (error) {
                            logger.error('Global notification callback error', { error: error.message });
                        }
                    });
                });
            }

        } catch (error) {
            logger.error('Failed to notify subscribers', { error: error.message });
        }
    }

    // Send email notification
    async sendEmailNotification(notification) {
        try {
            if (!this.emailEnabled) {
                return false;
            }

            // TODO: Implement email sending
            // This would require nodemailer or similar library
            logger.info('Email notification would be sent', {
                id: notification.id,
                type: notification.type,
                title: notification.title
            });

            return true;

        } catch (error) {
            logger.error('Failed to send email notification', { error: error.message });
            return false;
        }
    }

    // Predefined notification types
    async notifyLicenseExpiring(licenseData, daysLeft) {
        return await this.createNotification({
            type: 'warning',
            title: 'تنبيه انتهاء الترخيص',
            message: `ترخيصك سينتهي خلال ${daysLeft} أيام`,
            userId: licenseData.user_id,
            data: {
                licenseId: licenseData.id,
                licenseKey: licenseData.license_key,
                expiresAt: licenseData.expires_at,
                daysLeft: daysLeft
            },
            priority: 'high',
            sendEmail: true
        });
    }

    async notifyLicenseActivated(licenseData) {
        return await this.createNotification({
            type: 'success',
            title: 'تم تفعيل الترخيص',
            message: 'تم تفعيل ترخيصك بنجاح',
            userId: licenseData.user_id,
            data: {
                licenseId: licenseData.id,
                licenseKey: licenseData.license_key,
                activatedAt: new Date().toISOString()
            },
            priority: 'normal',
            sendEmail: true
        });
    }

    async notifySystemMaintenance(message, scheduledTime) {
        return await this.createNotification({
            type: 'info',
            title: 'صيانة النظام',
            message: message,
            userId: null, // Global notification
            data: {
                scheduledTime: scheduledTime,
                type: 'maintenance'
            },
            priority: 'normal'
        });
    }

    async notifySecurityAlert(userId, alertType, details) {
        return await this.createNotification({
            type: 'error',
            title: 'تنبيه أمني',
            message: `تم اكتشاف نشاط مشبوه: ${alertType}`,
            userId: userId,
            data: {
                alertType: alertType,
                details: details,
                timestamp: new Date().toISOString()
            },
            priority: 'critical',
            sendEmail: true
        });
    }

    // Utility methods
    generateId() {
        return 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async saveToDatabase(notification) {
        try {
            const db = getDatabase();

            return new Promise((resolve, reject) => {
                db.run(`
                    INSERT INTO notifications 
                    (id, type, title, message, user_id, data, read, created_at, expires_at, priority) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    notification.id,
                    notification.type,
                    notification.title,
                    notification.message,
                    notification.userId,
                    JSON.stringify(notification.data),
                    notification.read ? 1 : 0,
                    notification.created_at,
                    notification.expires_at,
                    notification.priority
                ], function(err) {
                    if (err) {
                        reject(err);
                        return;
                    }
                    resolve(this.lastID);
                });
            });

        } catch (error) {
            logger.error('Failed to save notification to database', { error: error.message });
            throw error;
        }
    }

    cleanupOldNotifications() {
        try {
            const db = getDatabase();
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 30); // Keep notifications for 30 days

            db.run(
                'DELETE FROM notifications WHERE created_at < ? AND read = 1',
                [cutoffDate.toISOString()],
                function(err) {
                    if (err) {
                        logger.error('Failed to cleanup old notifications', { error: err.message });
                        return;
                    }

                    if (this.changes > 0) {
                        logger.info('Old notifications cleaned up', { count: this.changes });
                    }
                }
            );

        } catch (error) {
            logger.error('Notification cleanup error', { error: error.message });
        }
    }

    // Initialize notifications table
    async initializeTable() {
        try {
            const db = getDatabase();

            return new Promise((resolve, reject) => {
                db.run(`
                    CREATE TABLE IF NOT EXISTS notifications (
                        id TEXT PRIMARY KEY,
                        type TEXT NOT NULL,
                        title TEXT NOT NULL,
                        message TEXT NOT NULL,
                        user_id INTEGER,
                        data TEXT,
                        read BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        expires_at DATETIME,
                        priority TEXT DEFAULT 'normal',
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                `, (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    logger.info('Notifications table initialized');
                    resolve();
                });
            });

        } catch (error) {
            logger.error('Failed to initialize notifications table', { error: error.message });
            throw error;
        }
    }
}

const notificationManager = new NotificationManager();

// Export both the manager instance and the initialize function
module.exports = notificationManager;
module.exports.initializeTable = initializeTable;
